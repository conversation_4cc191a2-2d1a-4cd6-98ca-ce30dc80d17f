<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IPlusMonthMemberRenewalPlanMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.juzifenqi.plus.module.order.repository.po.PlusMonthMemberRenewalPlanPo">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="user_id" property="userId" jdbcType="INTEGER"/>
        <result column="channel_id" property="channelId" jdbcType="INTEGER"/>
        <result column="order_sn" property="orderSn" jdbcType="VARCHAR"/>
        <result column="month_no" property="monthNo" jdbcType="VARCHAR"/>
        <result column="month_period" property="monthPeriod" jdbcType="INTEGER"/>
        <result column="current_period" property="currentPeriod" jdbcType="INTEGER"/>
        <result column="month_order_amount" property="monthOrderAmount" jdbcType="DECIMAL"/>
        <result column="program_id" property="programId" jdbcType="INTEGER"/>
        <result column="plan_state" property="planState" jdbcType="TINYINT"/>
        <result column="supplier_id" property="supplierId" jdbcType="INTEGER"/>
        <result column="plan_time" property="planTime" jdbcType="TIMESTAMP"/>
        <result column="actual_plan_time" property="actualPlanTime" jdbcType="TIMESTAMP"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, user_id, channel_id, order_sn, month_no, month_period, current_period,
        month_order_amount, program_id, plan_state, supplier_id, plan_time,
        actual_plan_time, remark, create_time, update_time
    </sql>

    <!-- 新增返回ID -->
    <insert id="save" parameterType="com.juzifenqi.plus.module.order.repository.po.PlusMonthMemberRenewalPlanPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_month_member_renewal_plan (
            user_id, channel_id, order_sn, month_no, month_period, current_period,
            month_order_amount, program_id, plan_state, supplier_id, plan_time,
            actual_plan_time, remark, create_time, update_time
        ) VALUES (
            #{userId}, #{channelId}, #{orderSn}, #{monthNo}, #{monthPeriod}, #{currentPeriod},
            #{monthOrderAmount}, #{programId}, #{planState}, #{supplierId}, #{planTime},
            #{actualPlanTime}, #{remark}, NOW(), NOW()
        )
    </insert>

    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO plus_month_member_renewal_plan (
            user_id, channel_id, order_sn, month_no, month_period, current_period,
            month_order_amount, program_id, plan_state, supplier_id, plan_time,
            actual_plan_time, remark, create_time, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.userId}, #{item.channelId}, #{item.orderSn}, #{item.monthNo}, 
                #{item.monthPeriod}, #{item.currentPeriod}, #{item.monthOrderAmount}, 
                #{item.programId}, #{item.planState}, #{item.supplierId}, #{item.planTime},
                #{item.actualPlanTime}, #{item.remark}, NOW(), NOW()
            )
        </foreach>
    </insert>

    <!-- 根据订单号和状态查询 -->
    <select id="getByOrderSnAndStates" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM plus_month_member_renewal_plan
        WHERE order_sn = #{orderSn}
        <if test="planStates != null and planStates.size() > 0">
            AND plan_state IN
            <foreach collection="planStates" item="state" open="(" separator="," close=")">
                #{state}
            </foreach>
        </if>
        ORDER BY id DESC
    </select>

    <!-- 分页查询 -->
    <select id="pageQuery" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM plus_month_member_renewal_plan
        <where>
            <if test="planTime != null">
                AND plan_time &gt;= #{planTime}
            </if>
            <if test="planStates != null and planStates.size() > 0">
                AND plan_state IN
                <foreach collection="planStates" item="state" open="(" separator="," close=")">
                    #{state}
                </foreach>
            </if>
        </where>
        ORDER BY id DESC
    </select>

    <!-- 查询总数 -->
    <select id="countQuery" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM plus_month_member_renewal_plan
        <where>
            <if test="planTime != null">
                AND plan_time &gt;= #{planTime}
            </if>
            <if test="planStates != null and planStates.size() > 0">
                AND plan_state IN
                <foreach collection="planStates" item="state" open="(" separator="," close=")">
                    #{state}
                </foreach>
            </if>
        </where>
    </select>

</mapper>
