<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.asserts.repository.dao.IPlusUseProfitSuMapper">

    <resultMap id="PlusUseProfitSu" type="com.juzifenqi.plus.module.asserts.repository.po.PlusUseProfitSuPo" >
        <result column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="channel_id" property="channelId" />
        <result column="order_sn" property="orderSn" />
        <result column="order_status" property="orderStatus" />
        <result column="plus_order_sn" property="plusOrderSn" />
        <result column="config_id" property="configId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="buy_card_type" property="buyCardType" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `user_id`,
        `channel_id`,
        `order_sn`,
        `order_status`,
        `plus_order_sn`,
        `config_id`,
        `create_time`,
        `update_time`,
        `buy_card_type`
    </sql>

    <insert id="savePlusUseProfitSu" parameterType="com.juzifenqi.plus.module.asserts.repository.po.PlusUseProfitSuPo" useGeneratedKeys="true" keyProperty="id">
      INSERT INTO plus_use_profit_su (
              `user_id`,
              `channel_id`,
              `order_sn`,
              `order_status`,
              `plus_order_sn`,
              `config_id`,
              `create_time`,
              `buy_card_type`
      )
      VALUES(
                #{userId},
                #{channelId},
                #{orderSn},
                #{orderStatus},
                #{plusOrderSn},
                #{configId},
                  NOW(),
                #{buyCardType}
      )
    </insert>


    <delete id="deletePlusUseProfitSu" parameterType="java.lang.Integer">
        DELETE FROM plus_use_profit_su
        WHERE `id` = #{id}
    </delete>

    <update id="updatePlusUseProfitSu" parameterType="com.juzifenqi.plus.module.asserts.repository.po.PlusUseProfitSuPo" >
        UPDATE plus_use_profit_su
        SET
        <if test="plusUseProfitSu.userId != null">`user_id`= #{plusUseProfitSu.userId},</if>
        <if test="plusUseProfitSu.channelId != null">`channel_id`= #{plusUseProfitSu.channelId},</if>
        <if test="plusUseProfitSu.orderSn != null">`order_sn`= #{plusUseProfitSu.orderSn},</if>
        <if test="plusUseProfitSu.orderStatus != null">`order_status`= #{plusUseProfitSu.orderStatus},</if>
        <if test="plusUseProfitSu.plusOrderSn != null">`plus_order_sn`= #{plusUseProfitSu.plusOrderSn},</if>
        <if test="plusUseProfitSu.configId != null">`config_id`= #{plusUseProfitSu.configId},</if>
        update_time = now()
        WHERE `id` = #{plusUseProfitSu.id}
    </update>


    <select id="loadPlusUseProfitSu" parameterType="java.lang.Integer" resultMap="PlusUseProfitSu">
        SELECT <include refid="Base_Column_List" />
        FROM plus_use_profit_su
        WHERE `id` = #{id}
    </select>

    <select id="pageList" parameterType="java.util.Map" resultMap="PlusUseProfitSu">
        SELECT <include refid="Base_Column_List" />
        FROM plus_use_profit_su
        LIMIT #{offset}, #{pagesize}
    </select>

    <select id="pageListCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT count(1)
        FROM plus_use_profit_su
    </select>


    <select id="loadPlusOrder" resultMap="PlusUseProfitSu">
        SELECT <include refid="Base_Column_List" />
        FROM plus_use_profit_su
        WHERE `plus_order_sn` = #{plusOrderSn}
            and order_sn = #{orderSn}
    </select>

    <select id="selectByUserId" parameterType="java.lang.Integer" resultMap="PlusUseProfitSu">
        SELECT <include refid="Base_Column_List" />
        FROM plus_use_profit_su
        WHERE `user_id` = #{userId}
    </select>
    
    <select id="selectByPlusOrderSn" parameterType="java.lang.String" resultMap="PlusUseProfitSu">
        SELECT <include refid="Base_Column_List" />
        FROM plus_use_profit_su
        WHERE `plus_order_sn` = #{plusOrderSn}
        LIMIT 1
    </select>

    <select id="selectByPlusOrderSnCommon" parameterType="java.lang.String" resultMap="PlusUseProfitSu">
        SELECT <include refid="Base_Column_List" />
        FROM plus_use_profit_su
        WHERE `plus_order_sn` = #{plusOrderSn}
    </select>

</mapper>
