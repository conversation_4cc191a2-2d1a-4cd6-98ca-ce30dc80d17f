package com.juzifenqi.plus.module.order.model.impl.strategy.cancel;


import com.juzifenqi.plus.enums.BlackListTypeEnum;
import com.juzifenqi.plus.enums.JuziPlusEnum;
import com.juzifenqi.plus.enums.PlusOrderPayTypeEnum;
import com.juzifenqi.plus.enums.PlusOrderStateEnum;
import com.juzifenqi.plus.module.common.IPlusBlackRepository;
import com.juzifenqi.plus.module.common.entity.PlusMemberBlackEntity;
import com.juzifenqi.plus.module.order.model.PlusOrderMsgNoticeModel;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderDeductPlanRepository;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderRepository;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderShuntRepository;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderCancelEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderCancelEvent;
import com.juzifenqi.plus.module.order.repository.po.PlusDeductLogPo;
import com.juzifenqi.plus.module.order.repository.po.PlusOrderShuntPo;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * 取消方式策略
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/15 14:53
 */
@Slf4j
public abstract class CancelTypeStrategyHandler {

    @Autowired
    private   IPlusBlackRepository           blackRepository;
    @Autowired
    private   IPlusOrderDeductPlanRepository deductPlanRepository;
    @Autowired
    protected IPlusOrderRepository           orderRepository;
    @Autowired
    protected IPlusOrderShuntRepository      plusOrderShuntRepository;
    @Autowired
    protected PlusOrderMsgNoticeModel        msgNoticeModel;

    /**
     * 当前取消方式 取消订单后处理逻辑
     */
    public void cancelOrderAfter(PlusOrderEntity plusOrderEntity, PlusOrderCancelEvent event,
            PlusOrderCancelEntity cancelEntity) {
    }


    /**
     * 加入后付款黑名单
     */
    protected void saveAfterOrderBlackList(PlusOrderEntity plusOrderEntity) {
        String orderSn = plusOrderEntity.getOrderSn();
        Integer userId = plusOrderEntity.getUserId();
        log.info("加入后付款黑名单开始：{}", orderSn);
        // 加入后付款黑名单
        // 当前会员类型是否是 桔享卡，提额卡，新人卡
        List<Integer> configList = Arrays.asList(JuziPlusEnum.NEW_JUXP_CARD.getCode(),
                JuziPlusEnum.QUOTA_CARD.getCode(), JuziPlusEnum.SUCCESS_CARD.getCode(),
                JuziPlusEnum.YITONG_CARD.getCode(), JuziPlusEnum.HYYK_CARD.getCode());
        if (!configList.contains(plusOrderEntity.getConfigId())) {
            return;
        }
        // 当前订单是否是后付款订单
        if (!PlusOrderPayTypeEnum.PAY_AFTER.getValue().equals(plusOrderEntity.getPayType())) {
            return;
        }
        // 是否支付成功
        if (plusOrderEntity.getOrderState() != PlusOrderStateEnum.PAY_SUCCESS.getCode()) {
            return;
        }
        //是否划扣支付
        List<PlusDeductLogPo> list = deductPlanRepository.getDeductLog(orderSn, userId);
        //过滤出划扣成功过的信息
        List<PlusDeductLogPo> deductLogs = list.stream()
                .filter(e -> e.getDeductStatus() != null && e.getDeductStatus() == 1)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(deductLogs)) {
            return;
        }
        //条件满足加入后付款黑名单
        PlusMemberBlackEntity plusPayBlackList = new PlusMemberBlackEntity();
        plusPayBlackList.setBlackType(BlackListTypeEnum.BLACK_TYPE_1.getCode());
        plusPayBlackList.setChannelId(plusOrderEntity.getChannelId());
        plusPayBlackList.setConfigId(plusOrderEntity.getConfigId());
        plusPayBlackList.setUserId(userId);
        blackRepository.saveBlackRecord(plusPayBlackList);
        log.info("加入后付款黑名单完成：{}", orderSn);
    }

    /**
     * 统一后置处理
     */
    protected void cancelAfterCommon(PlusOrderEntity plusOrderEntity, PlusOrderCancelEvent event,
            PlusOrderCancelEntity cancelEntity) {
        // 恢复分流计划的订单笔数和金额
        plusOrderShuntRepository.revertPlanShuntAmountAndCount(plusOrderEntity);
        // 发送相关通知
        msgNoticeModel.sendCancelNotice(event, plusOrderEntity);
    }
}
