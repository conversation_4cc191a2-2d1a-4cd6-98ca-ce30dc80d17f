package com.juzifenqi.plus.module.asserts.application.impl;

import com.juzifenqi.plus.dto.req.member.MemberQueryReq;
import com.juzifenqi.plus.enums.JuziPlusEnum;
import com.juzifenqi.plus.enums.PushTypeEnum;
import com.juzifenqi.plus.module.asserts.application.IMemberPlusQueryApplication;
import com.juzifenqi.plus.module.asserts.application.converter.IMemberPlusApplicationConverter;
import com.juzifenqi.plus.module.asserts.application.validator.MemberPlusQueryValidator;
import com.juzifenqi.plus.module.asserts.model.MemberPlusQueryModel;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusConfigDetailEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusConfigEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusInfoDetailEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusInfoEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusOpenTimeEntity;

import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 会员查询
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/2 18:27
 */
@Service
@Slf4j
public class MemberPlusQueryApplicationImpl implements IMemberPlusQueryApplication {

    @Autowired
    private MemberPlusQueryModel     queryModel;
    @Resource
    private MemberPlusQueryValidator validator;

    private final IMemberPlusApplicationConverter converter = IMemberPlusApplicationConverter.instance;

    @Override
    public MemberPlusInfoEntity getMemberPlusInfo(Integer userId, Integer channelId,
            Integer configId) {
        return queryModel.getMemberPlusInfo(userId, channelId, configId);
    }

    /**
     * 已购会员合集查询接口
     */
    @Override
    public List<MemberPlusInfoEntity> getMemberPlusInfoList(MemberQueryReq req) {
        validator.checkQueryMemberParams(req);
        return queryModel.getMemberPlusInfoCacheList(req);
    }

    /**
     * 获取用户会员类型 风控调用
     */
    @Override
    public Map<String, Object> getMemberType(MemberQueryReq req) {
        List<MemberPlusInfoEntity> entityList = this.getMemberPlusInfoList(req);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("isPlus", "0");
        resultMap.put("plusType", "0");
        if (!CollectionUtils.isEmpty(entityList)) {
            resultMap.put("isPlus", "1");
            entityList.forEach(info -> {
                PushTypeEnum plusById = PushTypeEnum.getPlusById(info.getConfigId());
                if (Objects.nonNull(plusById)) {
                    resultMap.put(plusById.getName(), plusById.getPlusType());
                }
            });
        }
        return resultMap;
    }

    /**
     * 已购会员卡合集查询 风控调用
     */
    @Override
    public MemberPlusConfigEntity getMemberPlusConfigInfo(MemberQueryReq req) {
        List<MemberPlusInfoEntity> entityList = this.getMemberPlusInfoList(req);
        MemberPlusConfigEntity configEntity = new MemberPlusConfigEntity();
        configEntity.setIsPlus(!CollectionUtils.isEmpty(entityList) ? 1 : 0);
        List<MemberPlusConfigDetailEntity> detailEntityList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(entityList)) {
            entityList.forEach(info -> {
                MemberPlusConfigDetailEntity plusInfoVo = new MemberPlusConfigDetailEntity();
                //获取会员方案名称
                plusInfoVo.setConfigId(info.getConfigId());
                plusInfoVo.setTypeName(JuziPlusEnum.getNameByCode(info.getConfigId()));
                PushTypeEnum plusById = PushTypeEnum.getPlusById(info.getConfigId());
                if (Objects.nonNull(plusById)) {
                    plusInfoVo.setTypeCode(plusById.getPlusType());
                }
                detailEntityList.add(plusInfoVo);
            });
        }
        String plusCodeBuf = detailEntityList.stream()
                .map(MemberPlusConfigDetailEntity::getTypeCode).collect(Collectors.joining(","));
        configEntity.setPlusCodeBuf(plusCodeBuf);
        configEntity.setPlusList(detailEntityList);
        return configEntity;
    }


    /**
     * 查询最后一次开通会员的开通时间（后付款取会员订单创建时间，非后付款取会员订单支付成功时间） 目前是固额卡、桔享卡、加速卡
     * <p>查询该用户id的所有有效会员订单，并根据订单的创建时间选出最近一条开通类型为桔享卡或固定提额卡或加速卡的订单，返回该订单的购买时间，时间精确度为年月日时分秒</p>
     */
    @Override
    public MemberPlusOpenTimeEntity getLastMemberOpenTime(MemberQueryReq req) {
        validator.checkQueryMemberParams(req);
        List<MemberPlusInfoDetailEntity> detailEntityList = queryModel.getMemberDetailListByUserId(
                req.getUserId(), Arrays.asList(JuziPlusEnum.NEW_JUXP_CARD.getCode(),
                        JuziPlusEnum.QUOTA_CARD.getCode(), JuziPlusEnum.YITONG_CARD.getCode(), JuziPlusEnum.HYYK_CARD.getCode(), JuziPlusEnum.EXPEDITE_CARD.getCode()));
        if (CollectionUtils.isEmpty(detailEntityList)) {
            log.info("查询最后一次开通会员的开通时间数据为空={}", req.getUserId());
            return null;
        }
        MemberPlusOpenTimeEntity result = new MemberPlusOpenTimeEntity();
        MemberPlusInfoDetailEntity detail = detailEntityList.get(0);
        result.setConfigId(detail.getConfigId());
        result.setOpenTime(detail.getCreateTime());
        return result;
    }

    /**
     * 查询用户开通最早的有效会员信息
     * <p>可能查出来的不是当前周期的，因为会员合并后周期都顺延了，如果用户当前会员类型有多条，则返回最早开通的数据</p>
     */
    @Override
    public MemberPlusInfoDetailEntity getFirstMemberInfo(MemberQueryReq req) {
        return queryModel.getUserFirstInfoCache(req);
    }

    /**
     * 校验用户是否会员身份(不包含融担卡)
     */
    @Override
    public Boolean checkIsAnyMemberPlus(MemberQueryReq req) {
        validator.checkQueryMemberParams(req);
        return queryModel.checkIsAnyMemberPlus(req);
    }

    /**
     * 根据会员单号查询会员周期信息
     */
    @Override
    public MemberPlusInfoDetailEntity getMemberPlusInfoDetail(String orderSn) {
        return queryModel.getMemberPlusInfoDetail(orderSn);
    }

    @Override
    public List<MemberPlusInfoDetailEntity> getMemberPlusInfoDetailList(MemberQueryReq req) {
        validator.checkQueryMemberParams(req);
        return queryModel.getMemberDetailList(req);
    }
}
