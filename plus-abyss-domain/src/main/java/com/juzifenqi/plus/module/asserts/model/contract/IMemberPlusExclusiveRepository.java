package com.juzifenqi.plus.module.asserts.model.contract;

import com.juzifenqi.plus.module.asserts.repository.po.PlusUseProfitSuPo;
import java.util.List;

/**
 * 用户专属权益记录
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/24 16:46
 */
public interface IMemberPlusExclusiveRepository {

    /**
     * 保存加速卡、新人卡专属权益信息
     */
    void savePlusUseProfitSuPo(PlusUseProfitSuPo po);

    /**
     * 根据用户id查询
     */
    List<PlusUseProfitSuPo> selectByUserId(Integer userId);
    
    /**
     * 根据会员订单号查询记录
     * 
     * @param plusOrderSn 会员订单号
     * @return 专属权益记录，不存在则返回null
     */
    PlusUseProfitSuPo selectByPlusOrderSn(String plusOrderSn);

    /**
     * 根据会员订单号查询记录(通用）
     *
     * @param plusOrderSn 会员订单号
     * @return 专属权益记录，不存在则返回null
     */
    List<PlusUseProfitSuPo> selectByPlusOrderSnCommon(String plusOrderSn);

}
