package com.juzifenqi.plus.module.order.model.impl.strategy;

import com.alibaba.fastjson.JSON;
import com.groot.utils.exception.LogUtil;
import com.juzifenqi.plus.constants.CommonConstant;
import com.juzifenqi.plus.enums.*;
import com.juzifenqi.plus.enums.pay.PayStateCodeEnum;
import com.juzifenqi.plus.enums.supplier.SupplierTypeEnum;
import com.juzifenqi.plus.exception.PlusAbyssException;
import com.juzifenqi.plus.module.asserts.model.MemberPlusQueryModel;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusInfoDetailEntity;
import com.juzifenqi.plus.module.common.IMemberSwitchControlRepository;
import com.juzifenqi.plus.module.common.IPlusBlackRepository;
import com.juzifenqi.plus.module.common.entity.MemberPlusSwitchControlEntity;
import com.juzifenqi.plus.module.common.entity.MemberPlusSystemLogEntity;
import com.juzifenqi.plus.module.common.entity.PlusMemberBlackEntity;
import com.juzifenqi.plus.module.common.entity.PlusMemberBlackLogEntity;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderRepository;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusDeductLogEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderDeductResEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.external.IOrderExternalRepository;
import com.juzifenqi.plus.module.order.model.event.PlusDeductEvent;
import com.juzifenqi.plus.module.order.model.event.PlusPayDeductEvent;
import com.juzifenqi.plus.module.order.model.event.PlusRealDeductEvent;
import com.juzifenqi.plus.module.order.model.event.order.LoanOrderCloseEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderCancelEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderCreateEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderDeductCallBackEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderPayDeductRespEvent;
import com.juzifenqi.plus.module.program.model.IPlusPriceConfigQueryModel;
import com.juzifenqi.plus.module.program.model.IPlusProgramQueryModel;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramEntity;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 降息卡
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/12 16:04
 */
@Slf4j
@Component
public class DownRatePlusHandler extends AbstractStrategyHandler {

    @Autowired
    private IMemberSwitchControlRepository switchControlRepository;
    @Autowired
    private IPlusBlackRepository           blackRepository;
    @Autowired
    private IOrderExternalRepository       iOrderExternalRepository;
    @Autowired
    private MemberPlusQueryModel           plusQueryModel;
    @Autowired
    private IPlusOrderRepository           orderRepository;
    @Autowired
    private IPlusPriceConfigQueryModel     plusPriceConfigQueryModel;
    @Autowired
    private IPlusProgramQueryModel         plusProgramQueryModel;

    @Override
    public void preProcessorAfterPlus(PlusOrderCreateEvent plusOrderCreateEvent,
            PlusProgramEntity plusProgramEntity) {
        Integer userId = plusOrderCreateEvent.getUserId();
        Integer configId = plusProgramEntity.getConfigId();
        Integer channelId = plusOrderCreateEvent.getChannelId();
        log.info("降息卡后付款开通前校验开始：{}", userId);
        List<MemberPlusInfoDetailEntity> list = plusQueryModel.getDetailByUserId(userId, configId);
        if (!CollectionUtils.isEmpty(list)) {
            throw new PlusAbyssException("您已经是降息卡会员,无法再次开通");
        }

        if (configProperties.authChannel.contains(channelId.toString())) {
            List<PlusOrderEntity> orderList = orderRepository.getPlusOrderByAuthTime(userId,
                    channelId, JuziPlusEnum.NEW_JUXP_CARD.getCode(), JuziPlusEnum.QUOTA_CARD.getCode(),
                    JuziPlusEnum.EXPEDITE_CARD.getCode(), JuziPlusEnum.REPAY_CARD.getCode(),
                    JuziPlusEnum.AUTH_CARD.getCode(), JuziPlusEnum.RESUBMIT_CARD.getCode(),
                    JuziPlusEnum.AUTHFAIL_CARD.getCode(), JuziPlusEnum.SUCCESS_CARD.getCode(),
                    JuziPlusEnum.DOWNRATE_CARD.getCode(), JuziPlusEnum.YITONG_CARD.getCode(),
                    JuziPlusEnum.HYYK_CARD.getCode());
            if (!CollectionUtils.isEmpty(orderList)) {
                throw new PlusAbyssException("认证成功后已购买过会员,无法再次开通");
            }
        } else {
            List<PlusOrderEntity> userSevenOrderList = orderRepository.getUserSevenOrderList(userId,
                    channelId, JuziPlusEnum.NEW_JUXP_CARD.getCode(), JuziPlusEnum.QUOTA_CARD.getCode(),
                    JuziPlusEnum.EXPEDITE_CARD.getCode(), JuziPlusEnum.REPAY_CARD.getCode(),
                    JuziPlusEnum.AUTH_CARD.getCode(), JuziPlusEnum.RESUBMIT_CARD.getCode(),
                    JuziPlusEnum.AUTHFAIL_CARD.getCode(), JuziPlusEnum.SUCCESS_CARD.getCode(),
                    JuziPlusEnum.DOWNRATE_CARD.getCode(), JuziPlusEnum.YITONG_CARD.getCode(),
                    JuziPlusEnum.HYYK_CARD.getCode());
            if (!CollectionUtils.isEmpty(userSevenOrderList)) {
                throw new PlusAbyssException("您七天内购买过会员,无法再次开通");
            }
        }

        Integer programId = plusPriceConfigQueryModel.getDefaultProgramId(channelId, configId);
        if (programId == null) {
            throw new PlusAbyssException("未配置默认方案,请联系客服处理");
        }
        PlusProgramEntity program = plusProgramQueryModel.getById(programId);
        if (program == null || program.getStatus() != CommonConstant.ONE) {
            throw new PlusAbyssException("默认方案未上架,请联系客服处理");
        }
        int count = plusProgramQueryModel.countProModelByProgramId(programId,
                PlusModelEnum.JX.getModelId());
        if (count < 1) {
            throw new PlusAbyssException("默认方案未配置降息权益,请联系客服处理");
        }
    }

    @Override
    public PlusOrderDeductResEntity deduct(PlusDeductEvent deductPlan) {
        log.info("降息卡划扣申请开始：{}", JSON.toJSONString(deductPlan));
        Integer userId = deductPlan.getUserId();
        PlusOrderEntity orderInfo = deductPlan.getPlusOrderEntity();
        String plusOrderSn = orderInfo.getOrderSn();
        PlusDeductLogTypeEnum logTypeEnum = PlusDeductLogTypeEnum.LOG_TYPE_10;
        PlusOrderDeductResEntity resEntity = new PlusOrderDeductResEntity();
        try {
            // 获取指定卡划扣申请
            PlusOrderDeductResEntity getResEntity = getDeductApplyEventByAppointCard(orderInfo,
                    deductPlan);
            if (!DeductResultStateEnum.SUCCESS.getCode().equals(getResEntity.getOptStatus())) {
                return getResEntity;
            }
            resEntity.setSeparateEntity(getResEntity.getSeparateEntity());
            // 组装划扣数据
            PlusPayDeductEvent deductEvent = converter.toPlusPayDeductEvent(
                    getResEntity.getSeparateEntity(), null);
            getResEntity.getSeparateEntity().getItems().forEach(item -> {
                // 只放清分主体
                if (item.getSupplierType().equals(SupplierTypeEnum.QF.getCode())) {
                    deductEvent.getSplitItems().add(converter.toPlusSplitItemEvent(item));
                }
            });
            PlusOrderPayDeductRespEvent respEvent = fmsRepository.deduct(deductEvent);
            resEntity.setDeductRespEvent(respEvent);
            if (!Objects.isNull(respEvent) && PayStateCodeEnum.I.getCode()
                    .equals(respEvent.getState())) {
                //成功
                log.info("降息卡后付款会员开通成功划扣申请成功：{}", plusOrderSn);
                resEntity.setRemark("划扣申请成功");
                resEntity.setOptStatus(DeductResultStateEnum.SUCCESS.getCode());
            } else {
                //失败
                log.info("降息卡后付款会员开通成功划扣申请失败：{}", plusOrderSn);
                resEntity.setRemark("划扣申请失败");
                resEntity.setOptStatus(DeductResultStateEnum.FAIL.getCode());
                logTypeEnum = PlusDeductLogTypeEnum.LOG_TYPE_2;
            }
        } catch (Exception e) {
            log.info("后付款降息卡会员划扣异常,plusOrderSn:{}", plusOrderSn, e);
            resEntity.setRemark("划扣申请异常");
            resEntity.setOptStatus(DeductResultStateEnum.ERROR.getCode());
            logTypeEnum = PlusDeductLogTypeEnum.LOG_TYPE_0;
            LogUtil.printLog("后付款降息卡待支付订单自动划扣申请失结束出现未知异常：", e);
        }
        PlusDeductLogEntity logEntity = converter.toPlusDeductLogEntity(deductPlan, logTypeEnum);
        deductPlanModel.saveDeductLog(logEntity);
        MemberPlusSystemLogEntity plusLog = new MemberPlusSystemLogEntity();
        String remark = "通过借款订单" + deductPlan.getOrderSn() + "触发划扣";
        plusLog.memberId(userId).channelId(orderInfo.getChannelId())
                .programId(orderInfo.getProgramId()).orderSn(plusOrderSn).cancelRemark(remark);
        systemLogRepository.saveMemberPlusLogBySystem(plusLog,
                LogNodeEnum.LOG_NODE_PLUS_DEDUCT_FEE);
        log.info("降息卡划扣申请完成：{}，{}，{}", userId, plusOrderSn, deductPlan.getOrderSn());
        return resEntity;
    }

    @Override
    @Deprecated
    public PlusOrderDeductResEntity deductOld(PlusDeductEvent deductPlan) {
        Integer userId = deductPlan.getUserId();
        PlusOrderEntity orderInfo = deductPlan.getPlusOrderEntity();
        String plusOrderSn = orderInfo.getOrderSn();
        PlusDeductLogTypeEnum logTypeEnum = PlusDeductLogTypeEnum.LOG_TYPE_10;
        PlusOrderDeductResEntity resEntity = new PlusOrderDeductResEntity();
        try {
            PlusRealDeductEvent realDeductEvent = new PlusRealDeductEvent();
            realDeductEvent.setMemberId(userId);
            realDeductEvent.setPlusOrderSn(plusOrderSn);
            realDeductEvent.setBankId(deductPlan.getBankId());
            realDeductEvent.setOrderAmount(orderInfo.getOrderAmount());
            PlusOrderDeductCallBackEvent payOrderVo = payExternalRepository.deduct(realDeductEvent);
            resEntity.setCallBackEvent(payOrderVo);
            if (!Objects.isNull(payOrderVo) && "S".equals(payOrderVo.getStatus())) {
                //成功
                log.info("降息卡后付款会员开通成功划扣成功：{}", plusOrderSn);
                resEntity.setRemark("同步划扣成功");
                resEntity.setOptStatus(DeductResultStateEnum.SUCCESS.getCode());
            } else {
                //失败
                log.info("降息卡后付款会员开通成功划扣失败：{}", plusOrderSn);
                resEntity.setRemark("同步划扣失败");
                resEntity.setOptStatus(DeductResultStateEnum.FAIL.getCode());
                logTypeEnum = PlusDeductLogTypeEnum.LOG_TYPE_2;
            }
        } catch (Exception e) {
            log.info("后付款降息卡会员划扣异常,plusOrderSn:{}", plusOrderSn, e);
            resEntity.setRemark("同步划扣异常");
            resEntity.setOptStatus(DeductResultStateEnum.ERROR.getCode());
            logTypeEnum = PlusDeductLogTypeEnum.LOG_TYPE_0;
            LogUtil.printLog("后付款降息卡待支付订单自动划扣结束出现未知异常：", e);
        }
        PlusDeductLogEntity logEntity = converter.toPlusDeductLogEntity(deductPlan, logTypeEnum);
        deductPlanModel.saveDeductLog(logEntity);
        MemberPlusSystemLogEntity plusLog = new MemberPlusSystemLogEntity();
        String remark = "通过借款订单" + deductPlan.getOrderSn() + "触发划扣";
        plusLog.memberId(userId).channelId(orderInfo.getChannelId())
                .programId(orderInfo.getProgramId()).orderSn(plusOrderSn).cancelRemark(remark);
        systemLogRepository.saveMemberPlusLogBySystem(plusLog,
                LogNodeEnum.LOG_NODE_PLUS_DEDUCT_FEE);
        return resEntity;
    }

    @Override
    public void cancelOrderAfter(PlusOrderEntity plusOrder, PlusOrderCancelEvent event) {
        log.info("降息卡取消会员后置逻辑处理：{}", plusOrder.getOrderSn());
        // 只有极速退款取消需要处理
        if (!Objects.equals(PlusCancelTypeEnum.JS.getValue(), event.getCancelType())) {
            log.info("降息卡，不满足的取消类型，无需处理 {}", event.getCancelType());
            return;
        }
        Integer userId = plusOrder.getUserId();
        //判断是否引流中原：引流中原才加中原黑名单
        if (!isLeadToZy(userId)) {
            log.info("降息卡极速退款后续处理开始-不引流-不加黑名单={}", userId);
            return;
        }
        PlusMemberBlackEntity plusPayBlackList = new PlusMemberBlackEntity();
        plusPayBlackList.setBlackType(BlackListTypeEnum.BLACK_TYPE_3.getCode());
        plusPayBlackList.setChannelId(plusOrder.getChannelId());
        plusPayBlackList.setConfigId(plusOrder.getConfigId());
        plusPayBlackList.setUserId(userId);
        blackRepository.saveBlackRecord(plusPayBlackList);
        PlusMemberBlackLogEntity blackLog = new PlusMemberBlackLogEntity();
        blackLog.setBlackType(BlackListTypeEnum.BLACK_TYPE_3.getCode());
        blackLog.setChannelId(plusOrder.getChannelId());
        blackLog.setConfigId(plusOrder.getConfigId());
        blackLog.setContent(userId + "");
        blackLog.setOptUserId(0);
        blackLog.setOptUserName("system");
        blackLog.setOptEvent(BlackListOptEventEnum.BLACK_OPT_4.getCode());
        blackLog.setEventName(BlackListOptEventEnum.BLACK_OPT_4.getName());
        blackRepository.saveBlackRecordLog(blackLog);
        log.info("降息卡极速退款后续处理结束，plusOrderSn:{}", plusOrder.getOrderSn());
    }

    @Override
    public void loanFkClose(LoanOrderCloseEvent event) {
        log.info("降息卡风控闭单处理开始：{}", JSON.toJSONString(event));
        createRapidPlusRefundRecord(event, PlusSwitchEnum.RATE_PlUS_REFUND.getCode());
        log.info("降息卡风控闭单处理完成：{},{}", event.getUserId(), event.getLoanOrderSn());
    }

    @Override
    public void loanZpClose(LoanOrderCloseEvent event) {
        log.info("降息卡资匹闭单处理开始：{}", JSON.toJSONString(event));
        createRapidPlusRefundRecord(event, PlusSwitchEnum.RATE_PlUS_REFUND.getCode());
        log.info("降息卡资匹闭单处理完成：{},{}", event.getUserId(), event.getLoanOrderSn());
    }

    @Override
    public void loanClose(LoanOrderCloseEvent event) {
        log.info("降息卡取消订单处理开始：{}", JSON.toJSONString(event));
        createRapidPlusRefundRecord(event, PlusSwitchEnum.RATE_PlUS_REFUND.getCode());
        log.info("降息卡取消订单处理完成：{},{}", event.getUserId(), event.getLoanOrderSn());
    }

    /**
     * 是否引流中原
     *
     * @param userId 用户id
     */
    private Boolean isLeadToZy(Integer userId) {
        //是否引流中原
        MemberPlusSwitchControlEntity controlPo = switchControlRepository.getSwitchByCode(
                PlusSwitchEnum.LEAD_ZY_SWITCH.getCode());
        if (Objects.isNull(controlPo)) {
            log.info("引流中原开关不存在 userId:{} ", userId);
            return false;
        }
        if (controlPo.getStatus() == CommonConstant.TWO) {
            log.info("引流中原开关未开启 userId:{}，status:{}", userId, controlPo.getStatus());
            return false;
        }
        //校验是否在中原黑名单
        if (blackRepository.inBlackList(userId, null, BlackListTypeEnum.BLACK_TYPE_3)) {
            log.info("引流中原-当前用户在中原黑名单中：{}", userId);
            return false;
        }
        if (!iOrderExternalRepository.checkStrikeBase(userId)) {
            log.info("引流中原-当前用户校验撞库规则不通过：{}", userId);
            return false;
        }
        log.info("引流中原校验通过userId:{}", userId);
        return true;
    }

}
