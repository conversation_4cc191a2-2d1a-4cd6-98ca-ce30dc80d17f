package com.juzifenqi.plus.module.order.repository.external.acl;
import static com.juzifenqi.plus.module.order.repository.external.acl.RdzxExternalRepositoryAcl.*;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.groot.utils.core.date.LocalDateTimeUtils;
import com.groot.utils.exception.LogUtil;
import com.groot.utils.http.OKHttp3SimpleUtils;
import com.groot.utils.http.OkHttpClientEnum;
import com.juzifenqi.plus.config.ConfigProperties;
import com.juzifenqi.plus.constants.CommonConstant;
import com.juzifenqi.plus.exception.PlusAbyssException;
import com.juzifenqi.plus.module.order.model.contract.entity.RateRespEntity;
import com.juzifenqi.plus.external.RmsExternal;
import com.juzifenqi.plus.module.common.IIMRepository;
import com.juzifenqi.plus.module.order.model.contract.entity.RateRespEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.RiskChangeUserEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.RiskFuseEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.RiskFuseEntity.MemberRaiseRespVO;
import com.juzifenqi.plus.module.order.model.contract.entity.RiskFuseEntity.RiskRaiseAmount199;
import com.juzifenqi.plus.module.order.model.contract.entity.RiskFuseEntity.RiskRaiseAmount399;
import com.juzifenqi.plus.module.order.model.contract.external.IRiskExternalRepository;
import com.jzsk.rms.vps.dto.domain.ResponseVo;
import com.jzsk.rms.vps.dto.domain.VpsReqParamsDto;
import com.jzsk.rms.vps.dto.domain.VpsReqVarDetailDto;
import java.math.BigDecimal;
import java.net.SocketTimeoutException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 风控实现
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/29 16:53
 */
@Service
@Slf4j
public class RiskExternalRepositoryAcl implements IRiskExternalRepository {

    @Autowired
    private ConfigProperties configProperties;

    @Autowired
    private RmsExternal   rmsExternal;
    @Autowired
    private IIMRepository iimRepository;

    /**
     * 接口成功码
     */
    public static final String SUC_CODE = "112200";
    /**
     * 接口异常码
     */
    public static final int    ERR_CODE = 10100001;

    /**
     * 会员等级199-021
     */
    public static final String GROUP_021 = "021";

    /**
     * 会员等级399-022
     */
    public static final String GROUP_022 = "022";
    /**
     * 会员等级-固定提额
     */
    public static final String GROUP_025 = "025";

    /**
     * 会员等级-固定提额
     */
    public static final String GROUP_YT = "15";

    /**
     * 会员等级-固定提额
     */
    public static final String GROUP_YK = "16";

    private static final Integer HTTP_RESPONSE_CODE_SUCCESS = 200;
    private static final Integer RATE_SUCCESS_CODE          = 200;

    @Override
    public RiskFuseEntity checkFused(Integer memberId, Integer channel, String grade) {
        RiskFuseEntity result = new RiskFuseEntity();
        try {
            long s = System.currentTimeMillis();
            log.info("======请求风控获取熔断接口开始,地址{}，memberId={},channel={},group={}",
                    configProperties.riskUrl + "/memberBiz/searchV2", memberId, channel, grade);
            // 20230328 zjf 替换http工具类
            String stringBuffer = "customerId" + "=" + memberId + "&" + "channelId" + "=" + channel;
            // 20230421 zjf 替换为如果调用异常-抛出异常的方法
            JSONObject riskResult = OKHttp3SimpleUtils.getByUrlException(
                    configProperties.riskUrl + "/memberBiz/searchV2/" + "?" + stringBuffer, null,
                    OkHttpClientEnum.TWO_SECOND, CommonConstant.ZERO);
            long e = System.currentTimeMillis();
            log.info("======请求风控获取熔断接口结束,耗时【{}ms】,接口返回:【{}】", e - s,
                    JSONObject.toJSONString(riskResult));
            JSONObject jsonObject = riskResult.getJSONObject("responseVal");
            if (jsonObject == null || !jsonObject.getString("code").equals(SUC_CODE)
                    || jsonObject.getString("data") == null) {
                throw new PlusAbyssException("请求风控获取熔断接口失败");
            }
            JSONObject jsonData = JSONObject.parseObject(jsonObject.getString("data"));
            log.info("风控熔断接口data:{}", jsonData);
            result.setFuse(jsonData.getBoolean("fuse"));
            result.setCanBuy(false);
            result.setNewComerGiftBagCardQuota(BigDecimal.ZERO);
            result.setRaiseAmount(BigDecimal.ZERO);
            result.setCurrentAssessTime(
                    LocalDateTimeUtils.parseStringToDate(jsonData.getString("currentAssessTime"),
                            LocalDateTimeUtils.DATE_FORMAT_FULL));
            result.setNextAssessTime(
                    LocalDateTimeUtils.parseStringToDate(jsonData.getString("nextAssessTime"),
                            LocalDateTimeUtils.DATE_FORMAT_FULL));
            log.info("调用风控查询接口返回已熔断 用户id:{} 返回fuse: {}", memberId,
                    result.getFuse());
            if (GROUP_021.equals(grade)) {
                String raiseAmount = jsonData.getString("raiseAmount199");
                if (StringUtils.isNotBlank(raiseAmount)) {
                    JSONObject raiseAmount199 = JSONObject.parseObject(raiseAmount);
                    result.setCanBuy(raiseAmount199.getBooleanValue("canBuy"));
                    result.setRaiseAmount(raiseAmount199.getBigDecimal("raiseAmount"));
                }
            } else if (GROUP_022.equals(grade)) {
                String raiseAmount = jsonData.getString("raiseAmount399");
                if (StringUtils.isNotBlank(raiseAmount)) {
                    JSONObject raiseAmount399 = JSONObject.parseObject(raiseAmount);
                    result.setCanBuy(raiseAmount399.getBooleanValue("canBuy"));
                    result.setRaiseAmount(raiseAmount399.getBigDecimal("raiseAmount"));
                }
            } else if (GROUP_025.equals(grade)) {
                String raiseAmount = jsonData.getString("raiseAmountCard");
                if (StringUtils.isNotBlank(raiseAmount)) {
                    JSONObject raiseAmountCard = JSONObject.parseObject(raiseAmount);
                    result.setCanBuy(raiseAmountCard.getBooleanValue("canBuy"));
                    result.setRaiseAmount(raiseAmountCard.getBigDecimal("raiseAmount"));
                }
            } else if (GROUP_YT.equals(grade)) {
                String raiseAmount = jsonData.getString("raiseAmountYiTongCard");
                if (StringUtils.isNotBlank(raiseAmount)) {
                    JSONObject raiseAmountCard = JSONObject.parseObject(raiseAmount);
                    result.setCanBuy(raiseAmountCard.getBooleanValue("canBuy"));
                    result.setRaiseAmount(raiseAmountCard.getBigDecimal("raiseAmount"));
                }
            }  else if (GROUP_YK.equals(grade)) {
                String raiseAmount = jsonData.getString("raiseAmountMonthCard");
                if (StringUtils.isNotBlank(raiseAmount)) {
                    JSONObject raiseAmountCard = JSONObject.parseObject(raiseAmount);
                    result.setCanBuy(raiseAmountCard.getBooleanValue("canBuy"));
                    result.setRaiseAmount(raiseAmountCard.getBigDecimal("raiseAmount"));
                }
            } else {
                String newComerQuota = jsonData.getString("newComerGiftBagCardQuota");
                if (StringUtils.isNotBlank(newComerQuota)) {
                    JSONObject newComerGiftBagCardQuota = JSONObject.parseObject(newComerQuota);
                    result.setCanBuy(newComerGiftBagCardQuota.getBooleanValue("canBuy"));
                    result.setNewComerGiftBagCardQuota(
                            newComerGiftBagCardQuota.getBigDecimal("raiseAmount"));
                }
            }
            //封装风控返回数据
            riskRaiseRespData(jsonData, result);
        } catch (Exception e) {
            result.setCode(ERR_CODE);
            // 20230421 zjf 超时异常
            if (e instanceof SocketTimeoutException || e instanceof TimeoutException) {
                log.info("请求风控获取熔断接口异常/超时", e);
            } else {
                LogUtil.printLog("判断用户能否购买会员提额方案异常", e);
            }
        }
        log.info("==========={}", JSON.toJSONString(result));
        return result;
    }

    @Override
    public RiskChangeUserEntity getUserInfo(Integer userId, Integer type) {
        String url = configProperties.rmsRateUrl + "/customerConvert/getConvertInfo";
        Map<String, Integer> params = new HashMap<>();
        params.put("customerId", userId);
        params.put("type", type);
        log.info("请求风控获取查询用户转换关系,地址：{},参数：{}", url, params);
        JSONObject result = OKHttp3SimpleUtils.postByJson(url, JSONObject.toJSONString(params),
                OkHttpClientEnum.TWO_SECOND);
        log.info("请求风控获取查询用户转换关系返回：{}",
                result != null ? result.toJSONString() : null);
        if (result == null) {
            log.info("查询用户转换关系，result为空");
            return null;
        }
        JSONObject responseVal = result.getJSONObject("responseVal");
        if (responseVal == null) {
            log.info("查询用户转换关系responseVal为空：{}", userId);
            return null;
        }
        if (!"200".equals(responseVal.getString("code"))) {
            log.info("查询用户转换关系，返回code==null或code!=200：{}", userId);
            return null;
        }
        Collection<JSONObject> data = responseVal.getObject("data", Collection.class);
        if (data == null || CollectionUtils.isEmpty(data)) {
            log.info("查询用户转换关系，返回结果data为空：{}", userId);
            return null;
        }
        JSONObject jsonTarget;
        // 默认是商城渠道
        if (type.equals(1)) {
            //1：商城查777 参数 channel = 777
            jsonTarget = data.stream()
                    .filter(jsonInfo -> CommonConstant.ORDER_CHANNEL_777 == jsonInfo.getInteger(
                            "targetChannelId")).findFirst().orElse(null);
        } else {
            //2：777查商城
            jsonTarget = data.stream().filter(jsonInfo -> CommonConstant.ONE == jsonInfo.getInteger(
                    "sourceChannelId")).findFirst().orElse(null);
        }
        if (jsonTarget == null) {
            log.info("查询用户转换关系，当前渠道数据为空：{}", userId);
            return null;
        }
        log.info("查询用户转换关系返回数据:{}", jsonTarget.toJSONString());
        return JSONObject.toJavaObject(jsonTarget, RiskChangeUserEntity.class);
    }

    @Override
    public List<String> getUserLevelList() {
        String url = configProperties.engineManageUrl + "/constant/queryListByTypeCode";
        Map<String, String> params = new HashMap<>(2);
        params.put("typeCode", "customerLevel");
        JSONObject result = OKHttp3SimpleUtils.postByJson(url, JSONObject.toJSONString(params),
                OkHttpClientEnum.TWO_SECOND);
        if (result == null) {
            throw new PlusAbyssException("请求获取用户等级列表接口失败");
        }
        log.info("获取用户等级列表，返回：{}", result.toJSONString());
        JSONObject responseVal = result.getJSONObject("responseVal");
        if (responseVal == null) {
            throw new PlusAbyssException("请求获取用户等级列表结果为空");
        }
        Collection<JSONObject> data = responseVal.getObject("data", Collection.class);
        return data.stream().map(e -> e.getString("constantValue")).collect(Collectors.toList());
    }

    @Override
    public String getUserLevel(Integer userId) {
        String url = configProperties.rmsRateUrl + "/riskAssessment/customerLevelByCustomerId";
        log.info("风控获取用户等级：userId：{}，url：{}", userId, url);
        Map<String, Object> map = new HashMap<>(2);
        map.put("customerId", userId);
        map.put("systemCode", "super-plus");
        JSONObject jsonObject = OKHttp3SimpleUtils.postByJson(url, JSONObject.toJSONString(map),
                OkHttpClientEnum.TWO_SECOND);
        if (jsonObject == null) {
            throw new PlusAbyssException("获取用户等级异常");
        }
        log.info("风控获取用户等级返回：{}", jsonObject.toJSONString());
        String string = jsonObject.getString("responseVal");
        JSONObject responseVal = JSONObject.parseObject(string);
        if (responseVal == null) {
            log.info("风控获取用户等级异常/超时：{}", userId);
            return null;
        }
        JSONObject data = responseVal.getJSONObject("data");
        if (data == null) {
            log.info("风控获取用户等级data为空：{}", userId);
            return null;
        }
        return data.getString("customerLevel");
    }

    @Override
    public RateRespEntity diffInterestRate(BigDecimal loanAmount, Integer userId,
            Integer channelId) {
        try {
            long s = System.currentTimeMillis();
            Map<String, Object> params = new HashMap<>(8);
            params.put("channelId", channelId);
            params.put("customerId", userId);
            //贷款类型 商品贷:02，现金贷:08，线下大额:0802
            params.put("loanType", "08");
            //金额
            params.put("applyAmount", loanAmount);
            String url = configProperties.rmsRateUrl + "/interestRate/fetchPeriodRate";
            String strParam = JSON.toJSONString(params);
            log.info("差异化利率查询开始,请求地址:{},请求参数:{}", url, strParam);
            JSONObject result = OKHttp3SimpleUtils.postByJson(url, strParam,
                    OkHttpClientEnum.TWO_SECOND);
            long e = System.currentTimeMillis();
            log.info("差异化利率查询结束,用户:{},耗时:{}ms,接口返回:{}", userId, e - s, result);

            if (result == null || !HTTP_RESPONSE_CODE_SUCCESS.equals(
                    result.getInteger("responseCode"))) {
                log.info("差异化利率查询,调用结果异常");
                throw new PlusAbyssException("差异化利率查询,调用结果异常");
            }
            JSONObject responseVal = result.getJSONObject("responseVal");
            if (responseVal == null || !RATE_SUCCESS_CODE.equals(responseVal.getInteger("code"))) {
                log.info("差异化利率查询,返回code或responseVal异常");
                throw new PlusAbyssException("返回code或responseVal异常");
            }
            JSONObject data = responseVal.getJSONObject("data");
            if (data == null) {
                log.info("差异化利率查询,返回date为空");
                throw new PlusAbyssException("返回date为空");
            }
            RateRespEntity rateRespDto = new RateRespEntity();
            rateRespDto.setLoanRate(data.getString("loanRate"));
            rateRespDto.setYLoanRate(data.getString("yLoanRate"));
            return rateRespDto;
        } catch (Exception e) {
            log.info("差异化利率查询系统异常,用户:{}", userId, e);
            return null;
        }
    }

    @Override
    public boolean getResubmitUser(Integer userId) {
        try {
            VpsReqParamsDto dto = new VpsReqParamsDto();
            VpsReqVarDetailDto detailDto = new VpsReqVarDetailDto();
            List<VpsReqVarDetailDto> params = new ArrayList<>();
            // 2个orderNo传同一个随机数即可，与安佳浩确认了
            long orderNo = new Random().nextLong();
            dto.setFkOrderNo(orderNo);
            dto.setOrderNo(String.valueOf(orderNo));
            dto.setSystem("ykd-plus-abyss");
            // 变量入参
            Map<String, Object> param = new HashMap<>(2);
            param.put("customerId", userId);
            detailDto.setInputParams(param);
            // 2个编码保持一致即可，与安佳浩确认了
            detailDto.setVariableCode(CommonConstant.RMS_RESUBMIT_USER_CODE);
            detailDto.setReqUniqueKey(CommonConstant.RMS_RESUBMIT_USER_CODE);
            params.add(detailDto);
            dto.setParams(params);
            log.info("调用风控是否重提用户入参：{}", JSON.toJSONString(dto));
            // 系统异常返回0或空字符串、变量数据没有(数据库查不到)返回0、超时返回-998，正常值返回0或1
            ResponseVo responseVo = rmsExternal.getVariable(dto);
            log.info("调用风控是否重提用户返回：{}", JSONObject.toJSONString(responseVo));
            if (responseVo == null || HttpStatus.SC_OK != responseVo.getCode()) {
                iimRepository.sendImMessage(
                        "调用风控查询是否重提用户返回失败，用户id：" + userId + "，变量code："
                                + CommonConstant.RMS_RESUBMIT_USER_CODE);
                return true;
            }
            if (Objects.isNull(responseVo.getData())) {
                iimRepository.sendImMessage(
                        "调用风控查询是否重提用户data返回空，用户id：" + userId + "，变量code："
                                + CommonConstant.RMS_RESUBMIT_USER_CODE);
                return true;
            }
            JSONArray array = JSONObject.parseArray(JSONObject.toJSONString(responseVo.getData()));
            String variableValue = array.getJSONObject(0).getString("variableValue");
            return StringUtils.isBlank(variableValue) || "1".equals(variableValue);
        } catch (Exception e) {
            LogUtil.printLog(e, "调用风控查询是否重提用户异常");
            iimRepository.sendImMessage("调用风控查询是否重提用户异常，用户id：" + userId + "，变量code："
                    + CommonConstant.RMS_RESUBMIT_USER_CODE);
            return true;
        }
    }


    /**
     * 封装风控返回数据
     */
    private void riskRaiseRespData(JSONObject jsonData, RiskFuseEntity result) {
        log.info("封装风控数据入参jsonData={},result={}", jsonData,
                JSONObject.toJSONString(result));
        //封装199数据信息
        String raiseAmount = jsonData.getString("raiseAmount199");
        if (StringUtils.isNotBlank(raiseAmount)) {
            JSONObject raiseAmount199 = JSONObject.parseObject(raiseAmount);
            RiskRaiseAmount199 riskRaiseAmount199 = new RiskRaiseAmount199();
            riskRaiseAmount199.setCanBuy(raiseAmount199.getBooleanValue("canBuy"));
            riskRaiseAmount199.setRaiseAmount(raiseAmount199.getBigDecimal("raiseAmount"));
            result.setRaiseAmount199(riskRaiseAmount199);
        }
        //封装399数据信息
        String raise399 = jsonData.getString("raiseAmount399");
        if (StringUtils.isNotBlank(raise399)) {
            JSONObject raiseAmount399 = JSONObject.parseObject(raise399);
            RiskRaiseAmount399 riskRaiseAmount399 = new RiskRaiseAmount399();
            riskRaiseAmount399.setCanBuy(raiseAmount399.getBooleanValue("canBuy"));
            riskRaiseAmount399.setRaiseAmount(raiseAmount399.getBigDecimal("raiseAmount"));
            result.setRaiseAmount399(riskRaiseAmount399);
        }
        MemberRaiseRespVO memberRaiseRespVO = new MemberRaiseRespVO();
        BeanUtils.copyProperties(result, memberRaiseRespVO);
        result.setMemberRaiseRespVO(memberRaiseRespVO);
        log.info("封装风控返回数据={}", JSONObject.toJSON(result));
    }
}
