package com.juzifenqi.plus.module.asserts.application.impl;

import com.alibaba.fastjson.JSON;
import com.groot.utils.exception.LogUtil;
import com.juzifenqi.member.entity.member.MemberInfo;
import com.juzifenqi.order.dao.entity.OrdersBase;
import com.juzifenqi.plus.enums.ChannelEnum;
import com.juzifenqi.plus.enums.JuziPlusEnum;
import com.juzifenqi.plus.enums.NewGradeEnum;
import com.juzifenqi.plus.enums.PlusSmsSendNodeEnum;
import com.juzifenqi.plus.module.asserts.application.IMemberPluSmsApplication;
import com.juzifenqi.plus.module.asserts.model.MemberPlusQueryModel;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusInfoDetailEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.RepayRenewRemindEntity;
import com.juzifenqi.plus.module.asserts.model.entity.asserts.CreditChangeEntity;
import com.juzifenqi.plus.module.common.IMemberExternalRepository;
import com.juzifenqi.plus.module.common.ISmsRepository;
import com.juzifenqi.plus.module.common.event.PlusSmsParamEvent;
import com.juzifenqi.plus.module.order.application.IPlusOrderRdzxApplication;
import com.juzifenqi.plus.module.order.model.PlusOrderQueryModel;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusRdzxRelationDetailEntity;
import com.juzifenqi.plus.module.order.model.contract.external.IOrderExternalRepository;
import com.juzifenqi.plus.pool.PlusThreadPool;
import com.juzishuke.credit.enums.CreditBizTypeEnum;
import java.math.BigDecimal;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 用户资产类短信发送
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/28 16:31
 */
@Slf4j
@Service
public class MemberPluSmsApplicationImpl implements IMemberPluSmsApplication {

    @Autowired
    private MemberPlusQueryModel      plusQueryModel;
    @Autowired
    private ISmsRepository            smsRepository;
    @Autowired
    private IMemberExternalRepository memberExternalRepository;
    @Autowired
    private IPlusOrderRdzxApplication rdzxApplication;
    @Autowired
    private IOrderExternalRepository  orderExternalRepository;
    @Autowired
    private PlusOrderQueryModel       orderQueryModel;

    @Override
    public void repayPlusRemindSms(String s) {
        log.info("还款卡续费短信提醒开始");
        List<RepayRenewRemindEntity> plusComingToEnd = plusQueryModel.getRepayExpireRenewList();
        if (CollectionUtils.isEmpty(plusComingToEnd)) {
            log.info("查询还款卡续费短信提醒数据为空");
            return;
        }
        // 20240724 ltq 会员任务处理效率优化,分批处理发送短信
        int size = StringUtils.isNotBlank(s) ? Integer.parseInt(s) : 200;
        List<List<RepayRenewRemindEntity>> partitions = ListUtils.partition(plusComingToEnd, size);
        partitions.forEach(partition -> PlusThreadPool.jobThreadPool.submit(() -> {
            log.info("异步处理还款卡续费短信提醒开始");
            partition.forEach(e -> {
                try {
                    //发送短信
                    PlusSmsParamEvent plusSmsParamVo = new PlusSmsParamEvent();
                    plusSmsParamVo.setUserId(e.getUserId());
                    plusSmsParamVo.setConfigId(JuziPlusEnum.REPAY_CARD.getCode());
                    // todo 目前没有还款卡先不处理
                    plusSmsParamVo.setChannelId(ChannelEnum.A.getCode());
                    plusSmsParamVo.setMallMobilePrice(e.getRenewPrice());
                    plusSmsParamVo.setSendNode(PlusSmsSendNodeEnum.NODE_3.getCode());
                    // 20230706 zjf 新增参数-会员短信二期
                    plusSmsParamVo.setProgramId(e.getProgramId());
                    smsRepository.sendSmsByConfig(plusSmsParamVo);
                } catch (Exception exception) {
                    LogUtil.printLog(exception, "还款卡续费短信提醒发送异常：" + e.getUserId());
                }
            });
            log.info("异步处理还款卡续费短信提醒结束");
        }));
        log.info("还款卡续费短信提醒结束");
    }

    @Override
    public void sendCreditChangeSms(CreditChangeEntity entity) {
        Integer customerId = entity.getCustomerId();
        Integer bizType = entity.getBizType();
        BigDecimal changeAmount = entity.getChangeAmount();
        List<String> changeItemCodeList = entity.getChangeItemCodeList();
        if (customerId == null || bizType == null || changeAmount == null
                || changeItemCodeList == null || changeItemCodeList.isEmpty()) {
            log.info("额度变更通知参数不全，处理结束");
            return;
        }
        if (CreditBizTypeEnum.CREDIT_ADD_TEMP.getCode() != bizType) {
            log.info("额度变更，业务类型不正确：{}", bizType);
            return;
        }
        MemberInfo member = memberExternalRepository.getById(customerId);
        if (member == null || StringUtils.isBlank(member.getRealName())) {
            log.info("额度变更，未查询到用户信息/用户姓名为空：{}", customerId);
            return;
        }
        Integer channelId = member.getChannelId();
        PlusSmsParamEvent smsParamEntity = new PlusSmsParamEvent();
        smsParamEntity.setUserId(customerId);
        smsParamEntity.setChannelId(channelId);
        smsParamEntity.setChangeAmount(changeAmount);
        smsParamEntity.setSendNode(PlusSmsSendNodeEnum.NOD_11.getCode());
        if (extracted(customerId, changeItemCodeList, member, channelId, smsParamEntity)) {
            return;
        }
        smsRepository.sendSmsByConfig(smsParamEntity);
        log.info("发送额度变更短信结束：{}", customerId);
    }

    /**
     * 处理发送降额短信逻辑
     */
    private boolean extracted(Integer customerId, List<String> changeItemCodeList,
            MemberInfo member, Integer channelId, PlusSmsParamEvent smsParamEntity) {
        // 融担咨询卡
        if (changeItemCodeList.contains(NewGradeEnum.MEMBER_RDZX_TEMP.getNewGrade())) {
            log.info("额度变更，开始处理融担咨询卡短信：{}", customerId);
            // 只查商城的
            PlusRdzxRelationDetailEntity relationDetail = rdzxApplication.getLastRelationDetail(
                    customerId, member.getChannelId());
            if (relationDetail == null || StringUtils.isBlank(relationDetail.getOrderSn())) {
                log.info("额度变更，未查询到融担咨询卡关联信息/借款单号为空：{}", customerId);
                return true;
            }
            OrdersBase ordersBase = orderExternalRepository.getOrdersBaseByOrdersNo(
                    relationDetail.getOrderSn());
            if (ordersBase == null) {
                log.info("额度变更，未查询到借款单信息：{}", relationDetail.getOrderSn());
                return true;
            }
            //成功之后发送短信
            smsParamEntity.setLoanOrderSn(relationDetail.getOrderSn());
            smsParamEntity.setConfigId(JuziPlusEnum.RDZX_CARD.getCode());
            smsParamEntity.setOrderSn(relationDetail.getPlusOrderSn());
            PlusOrderEntity plusOrderInfo = orderQueryModel.getByOrderSn(
                    relationDetail.getPlusOrderSn());
            if (plusOrderInfo != null) {
                smsParamEntity.setProgramId(plusOrderInfo.getProgramId());
            }
            smsParamEntity.setBankId(relationDetail.getBankId());
        } else if (changeItemCodeList.contains(NewGradeEnum.MEMBER_RE_QUOTA_TEMP.getNewGrade())) {
            // 固额卡
            log.info("额度变更，开始处理固额卡短信：{}", customerId);
            MemberPlusInfoDetailEntity memberPlusInfo = plusQueryModel.getUserLastInfo(customerId,
                    channelId, JuziPlusEnum.QUOTA_CARD.getCode());
            if (memberPlusInfo == null) {
                log.info("额度变更，未查询到固额卡信息：{}", customerId);
                return true;
            }
            smsParamEntity.setConfigId(JuziPlusEnum.QUOTA_CARD.getCode());
            smsParamEntity.setOrderSn(memberPlusInfo.getOrderSn());
            smsParamEntity.setProgramId(memberPlusInfo.getProgramId());
        } else if (changeItemCodeList.contains(NewGradeEnum.MEMBER_YT_TEMP.getNewGrade())) {
            // 固额卡
            log.info("额度变更，开始处理宜通卡短信：{}", customerId);
            MemberPlusInfoDetailEntity memberPlusInfo = plusQueryModel.getUserLastInfo(customerId,
                    channelId, JuziPlusEnum.YITONG_CARD.getCode());
            if (memberPlusInfo == null) {
                log.info("额度变更，未查询到宜通卡信息：{}", customerId);
                return true;
            }
            smsParamEntity.setConfigId(JuziPlusEnum.YITONG_CARD.getCode());
            smsParamEntity.setOrderSn(memberPlusInfo.getOrderSn());
            smsParamEntity.setProgramId(memberPlusInfo.getProgramId());
        } else if (changeItemCodeList.contains(NewGradeEnum.MEMBER_YK_TEMP.getNewGrade())) {
            // 固额卡
            log.info("额度变更，开始处理会员月卡短信：{}", customerId);
            MemberPlusInfoDetailEntity memberPlusInfo = plusQueryModel.getUserLastInfo(customerId,
                    channelId, JuziPlusEnum.HYYK_CARD.getCode());
            if (memberPlusInfo == null) {
                log.info("额度变更，未查询到会员月卡信息：{}", customerId);
                return true;
            }
            smsParamEntity.setConfigId(JuziPlusEnum.HYYK_CARD.getCode());
            smsParamEntity.setOrderSn(memberPlusInfo.getOrderSn());
            smsParamEntity.setProgramId(memberPlusInfo.getProgramId());
        } else if (changeItemCodeList.contains(NewGradeEnum.MEMBER_JSK_TEMP.getNewGrade())) {
            // 加速卡
            log.info("额度变更，开始处理加速卡短信：{}", customerId);
            MemberPlusInfoDetailEntity memberPlusInfo = plusQueryModel.getUserLastInfo(customerId,
                    channelId, JuziPlusEnum.EXPEDITE_CARD.getCode());
            if (memberPlusInfo == null) {
                log.info("额度变更，未查询到加速卡信息：{}", customerId);
                return true;
            }
            smsParamEntity.setConfigId(JuziPlusEnum.EXPEDITE_CARD.getCode());
            smsParamEntity.setOrderSn(memberPlusInfo.getOrderSn());
            smsParamEntity.setProgramId(memberPlusInfo.getProgramId());
        } else if (changeItemCodeList.contains(NewGradeEnum.MEMBER_199_GRADE.getNewGrade())
                || changeItemCodeList.contains(NewGradeEnum.MEMBER_399_GRADE.getNewGrade())) {
            // 桔享卡
            log.info("额度变更，开始处理桔享卡短信：{}", customerId);
            MemberPlusInfoDetailEntity memberPlusInfo = plusQueryModel.getUserLastInfo(customerId,
                    channelId, JuziPlusEnum.NEW_JUXP_CARD.getCode());
            if (memberPlusInfo == null) {
                log.info("额度变更，未查询到桔享卡信息：{}", customerId);
                return true;
            }
            smsParamEntity.setConfigId(JuziPlusEnum.NEW_JUXP_CARD.getCode());
            smsParamEntity.setOrderSn(memberPlusInfo.getOrderSn());
            smsParamEntity.setProgramId(memberPlusInfo.getProgramId());
        } else {
            log.info("额度变更，变更科目code不正确：{}", JSON.toJSONString(changeItemCodeList));
            return true;
        }
        return false;
    }
}
