package com.juzifenqi.plus.module.program.model.impl;

import com.groot.utils.exception.LogUtil;
import com.juzifenqi.member.entity.member.BaseEntity;
import com.juzifenqi.member.entity.member.MemberInfo;
import com.juzifenqi.order.dao.entity.Orders;
import com.juzifenqi.plus.config.ConfigProperties;
import com.juzifenqi.plus.constants.CommonConstant;
import com.juzifenqi.plus.constants.PlusConstant;
import com.juzifenqi.plus.constants.RedisConstantPrefix;
import com.juzifenqi.plus.dto.req.detail.LandReq;
import com.juzifenqi.plus.enums.*;
import com.juzifenqi.plus.exception.PlusAbyssException;
import com.juzifenqi.plus.module.asserts.model.MemberPlusQueryModel;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusInfoDetailEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusInfoEntity;
import com.juzifenqi.plus.module.asserts.model.event.model.HandleProfitLandEvent;
import com.juzifenqi.plus.module.asserts.model.impl.strategy.profits.ProfitHandlerContext;
import com.juzifenqi.plus.module.common.IAuthExternalRepository;
import com.juzifenqi.plus.module.common.IEngineExternalRepository;
import com.juzifenqi.plus.module.common.IMemberExternalRepository;
import com.juzifenqi.plus.module.common.IMemberSwitchControlRepository;
import com.juzifenqi.plus.module.common.IPlusBlackRepository;
import com.juzifenqi.plus.module.common.IPlusOrderExternalRepository;
import com.juzifenqi.plus.module.common.entity.MemberPlusSwitchControlEntity;
import com.juzifenqi.plus.module.order.model.PlusOrderModel;
import com.juzifenqi.plus.module.order.model.PlusOrderQueryModel;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusDiscountEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.external.IOrderExternalRepository;
import com.juzifenqi.plus.module.program.model.IPlusLandDetailModel;
import com.juzifenqi.plus.module.program.model.IPlusProfitQueryModel;
import com.juzifenqi.plus.module.program.model.IPlusProgramQueryModel;
import com.juzifenqi.plus.module.program.model.converter.IPlusProgramConverter;
import com.juzifenqi.plus.module.program.model.entity.detail.land.LandDetailEntity;
import com.juzifenqi.plus.module.program.model.entity.detail.land.LandModelBasicDetailEntity;
import com.juzifenqi.plus.module.program.model.entity.detail.land.LandOldDetailEntity;
import com.juzifenqi.plus.module.program.model.impl.strategy.AbstractHandler;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramEntity;
import com.juzifenqi.plus.utils.RedisUtils;
import com.jzfq.auth.core.entity.AuthApproval;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

/**
 * 落地页model
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/15 14:03
 */
@Slf4j
@Component
public class PlusLandDetailModelImpl implements IPlusLandDetailModel {

    private final IPlusProgramConverter converter = IPlusProgramConverter.instance;

    @Autowired
    private ProfitHandlerContext           handlerContext;
    @Autowired
    private AbstractHandler                handler;
    @Autowired
    private MemberPlusQueryModel           plusQueryModel;
    @Autowired
    private IPlusProgramQueryModel         programQueryModel;
    @Autowired
    private PlusOrderQueryModel            orderQueryModel;
    @Autowired
    private IPlusOrderExternalRepository   plusOrderExternalRepository;
    @Autowired
    private IPlusBlackRepository           blackRepository;
    @Autowired
    private IEngineExternalRepository      engineExternalRepository;
    @Autowired
    private IOrderExternalRepository       orderExternalRepository;
    @Autowired
    private IMemberSwitchControlRepository controlRepository;
    @Autowired
    private IPlusProfitQueryModel          profitQueryModel;
    @Autowired
    private IAuthExternalRepository        authExternalRepository;
    @Autowired
    private IMemberExternalRepository      memberExternalRepository;
    @Autowired
    private RedisUtils                     redisUtils;
    @Resource
    private ConfigProperties configProperties;
    @Autowired
    private PlusOrderModel plusOrderModel;

    @Override
    public LandDetailEntity getLandDetail(LandReq event) {
        return handler.getPlusService(event.getConfigId()).getLandDetail(event);
    }

    @Override
    public LandOldDetailEntity getOldLandDetail(LandReq event) {
        Integer userId = event.getUserId();
        Integer channelId = event.getChannelId();
        Integer configId = event.getConfigId();
        Integer programId = event.getProgramId();
        if (!configProperties.plusChannels.contains(String.valueOf(channelId))) {
            throw new PlusAbyssException("该渠道不展示落地页");
        }
        if (userId != null && userId != 0) {
            checkMemberPlus(userId, configId, channelId);
        }
        PlusProgramEntity program = programQueryModel.getById(programId);
        if (program == null || program.getProgramId() == null) {
            throw new PlusAbyssException("方案无效");
        }
        Date date = new Date();
        if (program.getEffectiveTime().after(date)) {
            throw new PlusAbyssException("方案未生效,暂不可购买");
        }
        if (program.getStatus() != 1) {
            throw new PlusAbyssException("方案未上架,暂不可购买");
        }
        LandOldDetailEntity entity = converter.toLandOldDetailEntity(program);
        entity.setIsLogin(userId != null ? 1 : 0);
        // 每日费用
        entity.setAmountPreDay(String.valueOf(program.getMallMobilePrice()
                .divide(new BigDecimal(program.getProgrammeDays()), 2, RoundingMode.HALF_UP)));
        //TODO  是否支持后付款
        /*boolean support = supportAfterPay(userId, channelId, programId, configId, true);
        entity.setAfterPayState(support ? 1 : 2);*/
        List<Integer> payTypes = supportAfterPayNew(userId, channelId, programId, configId, true);
        entity.setPayTypes(payTypes);
        if (!CollectionUtils.isEmpty(payTypes)) {
            entity.setAfterPayState(payTypes.contains(PlusOrderPayTypeEnum.PAY_AFTER.getValue()) ? 1 : 2);
        }
        // 折扣信息
        if (userId != null) {
            PlusDiscountEntity discount = plusOrderModel.getPlusDiscount(userId, configId, channelId, event.getSceneCode());
            if (discount != null) {
                entity.setShowDiscountInfo(discount.getShowDiscountInfo());
                entity.setDiscountRate(discount.getDiscountRate());
                if (discount.getDiscountEndTime() != null) {
                    entity.setDiscountEndTime(
                            discount.getDiscountEndTime().getTime() - date.getTime());
                }
                if (entity.getDiscountRate() != null && entity.getShowDiscountInfo() == 1) {
                    entity.setDiscountPrice(
                            program.getMallMobilePrice().multiply(entity.getDiscountRate())
                                    .setScale(0, RoundingMode.DOWN));
                    //计算首付折扣金额
                    if (entity.getPayTypes().contains(PlusOrderPayTypeEnum.PAY_FIRST_PERIOD.getValue())) {
                        if (null != entity.getFirstPayAmount() && entity.getFirstPayAmount().compareTo(BigDecimal.ZERO) > 0) {
                            entity.setFirstPayAmount(
                                    program.getFirstPayAmount().multiply(entity.getDiscountRate())
                                            .setScale(0, RoundingMode.FLOOR));
                        }
                        //首付剩余支付金额
                        BigDecimal surplusPayAmount = program.getMallMobilePrice().multiply(entity.getDiscountRate()).setScale(0, RoundingMode.FLOOR).subtract(entity.getFirstPayAmount());
                        entity.setSurplusPayAmount(surplusPayAmount);
                    }
                }
            }
        }
        if (userId != null) {
            // 认证状态
            AuthApproval authApproval = authExternalRepository.getAuthState(userId, channelId);
            entity.setAuthState(authApproval.getAuthState());
        }
        // 开关
        if (configId == JuziPlusEnum.SUCCESS_CARD.getCode()
                || configId == JuziPlusEnum.EXPEDITE_CARD.getCode()) {
            MemberPlusSwitchControlEntity switchControl = controlRepository.getSwitchByCode(
                    configId);
            entity.setPlusSwitch(switchControl != null ? switchControl.getStatus() : null);
        }
        // 虚拟权益信息
        entity.setVirtualProfits(profitQueryModel.getVirtualProductList(programId));
        // 是否显示会员权益页按钮
        List<MemberPlusInfoEntity> memberPlusInfoList = plusQueryModel.getMemberPlusInfoList(userId,
                channelId);
        if (!CollectionUtils.isEmpty(memberPlusInfoList)) {
            entity.setShowPlusButton(1);
        }
        // 权益列表
        List<LandModelBasicDetailEntity> profitList = getProfitList(event);
        entity.setModelBasicInfoVos(profitList);
        entity.setRightsNum(!CollectionUtils.isEmpty(profitList) ? profitList.size() : 0);
        // 会员提额标识
        Integer quotaOperate = getQuotaOperate(profitList);
        entity.setOperate(quotaOperate);
        // 是否可买
        boolean canBuy = canBuy(userId, configId, entity.getAuthState(), quotaOperate);
        entity.setCanBuy(canBuy ? 1 : 2);
        if (!canBuy) {
            entity.setOperate(-1);
        }
        // 不可购买的，设置不展示挽回弹出
        entity.setBeSetRecovery(canBuy ? null : 2);
        return entity;
    }

    @Override
    public LandOldDetailEntity getLandCommonDetail(LandReq event) {
        Integer userId = event.getUserId();
        Integer programId = event.getProgramId();
        Integer configId = event.getConfigId();
        Integer channelId = event.getChannelId();
        PlusProgramEntity program = programQueryModel.getById(programId);
        if (program == null || program.getProgramId() == null) {
            throw new PlusAbyssException("方案无效");
        }
        LandOldDetailEntity entity = converter.toLandOldDetailEntity(program);
        List<LandModelBasicDetailEntity> basics = profitQueryModel.getProfitModelList(programId);
        // 小额月卡不展示会员提额，跟落地页保持一致
        if (JuziPlusEnum.XEYK_CARD.getCode() == configId) {
            basics.removeIf(e -> e.getModelId() == PlusModelEnum.HYTE.getModelId());
        }
        entity.setModelBasicInfoVos(basics);
        // 折扣信息
        if (userId != null) {
            PlusDiscountEntity discount = plusOrderModel.getPlusDiscount(userId, configId, channelId, event.getSceneCode());
            if (discount != null) {
                entity.setShowDiscountInfo(discount.getShowDiscountInfo());
                entity.setDiscountRate(discount.getDiscountRate());
                if (discount.getDiscountEndTime() != null) {
                    entity.setDiscountEndTime(
                            discount.getDiscountEndTime().getTime() - new Date().getTime());
                }
                if (entity.getDiscountRate() != null && entity.getShowDiscountInfo() == 1) {
                    entity.setDiscountPrice(
                            program.getMallMobilePrice().multiply(entity.getDiscountRate())
                                    .setScale(0, RoundingMode.DOWN));
                }
            }
            //TODO 首付支付设置
            PlusProgramEntity programEntity = programQueryModel.supportPayTypes(programId, event.getUserId(),
                    event.getChannelId());
            entity.setPayTypes(programEntity.getPayTypes());
            entity.setFirstPayAmount(programEntity.getFirstPayAmount());
            if (!CollectionUtils.isEmpty(programEntity.getPayTypes())) {
                List<Integer> payTypes = programEntity.getPayTypes();
                if (payTypes.contains(PlusOrderPayTypeEnum.PAY_AFTER.getValue())) {
                    entity.setAfterPayState(1);
                } else {
                    entity.setAfterPayState(2);
                }
                //如果支持首期支付计算首付金额
                if (payTypes.contains(PlusOrderPayTypeEnum.PAY_FIRST_PERIOD.getValue())) {
                    if (discount != null && discount.getShowDiscountInfo() == 1) {
                        entity.setFirstPayAmount(
                                programEntity.getFirstPayAmount().multiply(discount.getDiscountRate())
                                        .setScale(0, RoundingMode.DOWN));
                    }
                }
            }
        }
        // 是否支持后付款
        /*boolean support = supportAfterPay(userId, channelId, programId, configId, false);
        entity.setAfterPayState(support ? 1 : 2);*/
        return entity;
    }

    @Override
    public List<String> getPlusBuyRecords(Integer programId) {
        try {
            log.info("获取购买记录开始：{}", programId);
            // 时间段判断，0-6点不展示
            SimpleDateFormat df = new SimpleDateFormat("HH");
            int hours = Integer.parseInt(df.format(new Date()));
            if (hours < CommonConstant.SHOW_RECORD_HOURS) {
                log.info("获取购买记录-不在时间范围内：{}", programId);
                return new ArrayList<>();
            }
            PlusProgramEntity program = programQueryModel.getById(programId);
            // 校验该方案是否展示购买记录
            if (program == null || program.getShowBuyRecord() == null
                    || program.getShowBuyRecord() == 0) {
                log.info("获取购买记录-方案未设置展示购买记录,programId：{}", programId);
                return new ArrayList<>();
            }
            // 缓存中获取数据
            String redisKey = RedisConstantPrefix.MEMBER_PLUS_BUY_RECORD + programId;
            List<String> redisList = redisUtils.lRange(redisKey, CommonConstant.REDIS_RANGE_START,
                    CommonConstant.REDIS_RANGE_END);
            if (CollectionUtils.isEmpty(redisList)
                    || redisList.size() < CommonConstant.RECORD_PAY_LIMIT) {
                List<PlusOrderEntity> list = orderQueryModel.getPayRecordByProgram(programId);
                log.info("数据库中获取购买记录：{},{}", programId, list);
                if (CollectionUtils.isEmpty(list)) {
                    return new ArrayList<>();
                }
                List<MemberInfo> memberInfoList = memberExternalRepository.getByIds(
                        list.stream().map(PlusOrderEntity::getUserId).distinct()
                                .collect(Collectors.toList()));
                Map<Integer, List<MemberInfo>> userMap = memberInfoList.stream()
                        .collect(Collectors.groupingBy(BaseEntity::getId));
                List<String> cacheList = list.stream().map(e -> {
                    List<MemberInfo> memberInfos = userMap.get(e.getUserId());
                    if (!CollectionUtils.isEmpty(memberInfos)) {
                        return "用户" + memberInfos.get(0).getName() + "已开通";
                    }
                    // 默认值
                    return "用户181****2021已开通";
                }).collect(Collectors.toList());
                //存到redis中
                if (!CollectionUtils.isEmpty(cacheList)) {
                    redisUtils.lLeftPushAll(redisKey, cacheList);
                }
                return cacheList;
            }
            log.info("缓存中获取购买记录返回：{},{}", programId, redisList);
            return redisList;
        } catch (Exception e) {
            LogUtil.printLog("获取购买记录发生异常", e);
            //直接返回空数据不影响主流程
            return new ArrayList<>();
        }
    }

    /**
     * 校验是否能买
     */
    private boolean canBuy(Integer userId, Integer configId, Integer authState,
            Integer quotaOperate) {
        if (userId == null) {
            return false;
        }
        log.info("落地页校验是否能买开始：{},{}", userId, configId);
        boolean b = blackRepository.inBlackList(userId, configId, BlackListTypeEnum.BLACK_TYPE_2);
        if (b) {
            log.info("落地页是否能买,用户在会员黑名单：{}", userId);
            return false;
        }
        if (configId != JuziPlusEnum.JUS_CARD.getCode() && authState != 1 && authState != 5) {
            log.info("落地页是否能买,非桔省卡且用户非认证成功：{},{},{}", userId, configId,
                    authState);
            return false;
        }
        if (quotaOperate != null && (quotaOperate == 0 || quotaOperate == -1)) {
            log.info("落地页是否能买,会员提额权益返回不能购买：{}", userId);
            return false;
        }
        log.info("落地页是否能买校验通过：{},{}", userId, configId);
        return true;
    }

    /**
     * 是否支持后付款
     */
    private boolean supportAfterPay(Integer userId, Integer channelId, Integer programId,
            Integer configId, boolean checkXeyk) {
        if (userId == null) {
            return false;
        }
        boolean support = programQueryModel.supportAfterPay(programId, userId, channelId);
        if (!support) {
            return false;
        }
        // 20230918 zjf 加速、固额、桔享：方案支持后付款 且 非小额月卡客群 且 非小额月卡会员才展示后付款按钮
        if ((JuziPlusEnum.NEW_JUXP_CARD.getCode() == configId
                || JuziPlusEnum.EXPEDITE_CARD.getCode() == configId
                || JuziPlusEnum.YITONG_CARD.getCode() == configId
                || JuziPlusEnum.HYYK_CARD.getCode() == configId
                || JuziPlusEnum.QUOTA_CARD.getCode() == configId) && checkXeyk) {
            log.info("落地页桔享,加速,固额是否小额月卡客群或会员,是否支持后付款校验：{}", userId);
            Orders orders = orderExternalRepository.lastOneProcessOrder(userId);
            boolean xeykGroup = engineExternalRepository.xeykGroup(
                    orders != null ? orders.getOrderSn() : null, userId, channelId);
            if (xeykGroup) {
                log.info("落地页桔享,加速,固额是小额月卡客群,不支持后付款：{}", userId);
                return false;
            }
            log.info("非小额月卡客群，继续校验是否小额月卡会员：{}", userId);
            MemberPlusInfoDetailEntity detail = plusQueryModel.getUserLastInfo(userId, channelId,
                    JuziPlusEnum.XEYK_CARD.getCode());
            if (detail != null) {
                log.info("落地页桔享,加速,固额是小额月卡会员,不支持后付款：{}", userId);
                return false;
            }
        }
        log.info("落地页后付款校验通过，用户和方案支持后付款：{}", userId);
        return true;
    }

    private List<Integer> supportAfterPayNew(Integer userId, Integer channelId, Integer programId,
                                    Integer configId, boolean checkXeyk) {
        if (userId == null) {
            return null;
        }
        PlusProgramEntity plusProgramEntity = programQueryModel.supportPayTypes(programId, userId, channelId);
        if (null == plusProgramEntity) {
            return null;
        }
        List<Integer> payTypes = plusProgramEntity.getPayTypes();
        if (CollectionUtils.isEmpty(payTypes)) {
            return null;
        }
        // 20230918 zjf 加速、固额、桔享：方案支持后付款 且 非小额月卡客群 且 非小额月卡会员才展示后付款按钮
        if ((JuziPlusEnum.NEW_JUXP_CARD.getCode() == configId
                || JuziPlusEnum.EXPEDITE_CARD.getCode() == configId
                || JuziPlusEnum.YITONG_CARD.getCode() == configId
                || JuziPlusEnum.QUOTA_CARD.getCode() == configId) && checkXeyk) {
            log.info("落地页桔享,加速,固额是否小额月卡客群或会员,是否支持后付款校验：{}", userId);
            Orders orders = orderExternalRepository.lastOneProcessOrder(userId);
            boolean xeykGroup = engineExternalRepository.xeykGroup(
                    orders != null ? orders.getOrderSn() : null, userId, channelId);
            if (xeykGroup) {
                log.info("落地页桔享,加速,固额是小额月卡客群,不支持后付款：{}", userId);
                payTypes.removeIf(num -> num == PlusOrderPayTypeEnum.PAY_AFTER.getValue());
                return payTypes;
            }
            log.info("非小额月卡客群，继续校验是否小额月卡会员：{}", userId);
            MemberPlusInfoDetailEntity detail = plusQueryModel.getUserLastInfo(userId, channelId,
                    JuziPlusEnum.XEYK_CARD.getCode());
            if (detail != null) {
                log.info("落地页桔享,加速,固额是小额月卡会员,不支持后付款：{}", userId);
                payTypes.removeIf(num -> num == PlusOrderPayTypeEnum.PAY_AFTER.getValue());
                return payTypes;
            }
        }
        log.info("落地页后付款校验通过，用户和方案支持后付款：{}", userId);
        return payTypes;
    }

    /**
     * 校验用户是否会员身份
     */
    private void checkMemberPlus(Integer userId, Integer configId, Integer channelId) {
        if (PlusConstant.MERGE_CARD_LIST.contains(configId)) {
            List<MemberPlusInfoDetailEntity> list = plusQueryModel.getDetailByUserId(userId,
                    configId);
            if (!CollectionUtils.isEmpty(list)) {
                throw new PlusAbyssException("300004",
                        "您已经是会员了，请到会员详情页查看享有的权益");
            }
        } else {
            MemberPlusInfoDetailEntity detail = plusQueryModel.getCurrentMemberDetail(userId,
                    configId);
            if (detail != null) {
                throw new PlusAbyssException("300004",
                        "您已经是会员了，请到会员详情页查看享有的权益");
            }
        }
    }

    /**
     * 获取权益列表
     */
    private List<LandModelBasicDetailEntity> getProfitList(LandReq event) {
        Integer programId = event.getProgramId();
        Integer configId = event.getConfigId();
        // 获取所有权益列表
        List<LandModelBasicDetailEntity> list = profitQueryModel.getProfitModelList(programId);
        // 加速卡、小额月卡、还款卡提额不展示提额权益（因为会员提额要走熔断，这三张卡可能会被熔断，导致无法购买，包括下单的时候也不会走熔断）
        if (JuziPlusEnum.EXPEDITE_CARD.getCode() != configId
                && JuziPlusEnum.XEYK_CARD.getCode() != configId
                && JuziPlusEnum.REPAY_CARD.getCode() != configId) {
            list.stream().filter(e -> e.getModelId() == PlusModelEnum.HYTE.getModelId())
                    .forEach(e -> {
                        HandleProfitLandEvent profitLandEvent = converter.toHandleProfitLandEvent(
                                event, PlusModelEnum.HYTE.getModelId());
                        e.setData(handlerContext.getProfitForOpen(profitLandEvent));
                    });
        } else {
            list.removeIf(e -> e.getModelId() == PlusModelEnum.HYTE.getModelId());
        }
        // 0元商品
        list.stream().filter(e -> e.getModelId() == PlusModelEnum.LYSP.getModelId()).forEach(e -> {
            HandleProfitLandEvent profitLandEvent = converter.toHandleProfitLandEvent(event,
                    PlusModelEnum.LYSP.getModelId());
            e.setData(handlerContext.getProfitForOpen(profitLandEvent));
        });
        list.sort(Comparator.comparing(LandModelBasicDetailEntity::getSort));
        return list;
    }

    /**
     * 获取会员提额权益返回的标识code
     */
    private Integer getQuotaOperate(List<LandModelBasicDetailEntity> list) {
        LandModelBasicDetailEntity quota = list.stream()
                .filter(e -> e.getModelId() == PlusModelEnum.HYTE.getModelId()).findFirst()
                .orElse(null);
        if (quota == null) {
            return null;
        }
        Map<String, Object> data = quota.getData();
        if (data == null || data.get("operate") == null) {
            return null;
        }
        return (Integer) data.get("operate");
    }
}
