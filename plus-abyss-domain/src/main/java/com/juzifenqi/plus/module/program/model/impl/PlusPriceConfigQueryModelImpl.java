package com.juzifenqi.plus.module.program.model.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.juzifenqi.order.dao.entity.Orders;
import com.juzifenqi.order.dto.OrderSimpleInfoDTO;
import com.juzifenqi.plus.constants.CommonConstant;
import com.juzifenqi.plus.constants.EngineCodeConstant;
import com.juzifenqi.plus.constants.PlusConstant;
import com.juzifenqi.plus.constants.RedisConstantPrefix;
import com.juzifenqi.plus.enums.JuziPlusEnum;
import com.juzifenqi.plus.enums.NewGradeEnum;
import com.juzifenqi.plus.module.asserts.model.contract.IMemberPlusInfoRepository;
import com.juzifenqi.plus.module.common.ICreditExternalRepository;
import com.juzifenqi.plus.module.common.IEngineExternalRepository;
import com.juzifenqi.plus.module.common.IUserSignExternalRepository;
import com.juzifenqi.plus.module.common.entity.DistributionMarketEntity;
import com.juzifenqi.plus.module.common.entity.UserSignInfoEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.RiskFuseEntity;
import com.juzifenqi.plus.module.order.model.contract.external.IOrderExternalRepository;
import com.juzifenqi.plus.module.order.model.contract.external.IRiskExternalRepository;
import com.juzifenqi.plus.module.program.model.IPlusPriceConfigQueryModel;
import com.juzifenqi.plus.module.program.model.contract.IPlusLiftAmountRepository;
import com.juzifenqi.plus.module.program.model.event.price.PriceConfigContext;
import com.juzifenqi.plus.module.program.model.event.price.PriceConfigQueryEvent;
import com.juzifenqi.plus.module.program.model.event.price.SaveDifferenceProgramPriceEvent;
import com.juzifenqi.plus.module.program.repository.po.price.PlusDefaultProgramPricePo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusLiftAmountPo;
import com.juzifenqi.plus.utils.RedisUtils;
import com.juzishuke.credit.request.CreditCalculateRequest;
import com.juzishuke.credit.vo.CreditCalculateVO;
import com.juzishuke.credit.vo.CustomerCreditChannelDetailVO;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 差异化/默认方案查询
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/10 15:28
 */
@Slf4j
@Component
public class PlusPriceConfigQueryModelImpl implements IPlusPriceConfigQueryModel {


    @Autowired
    private RedisUtils                  redisUtils;
    @Autowired
    private ICreditExternalRepository   creditExternalRepository;
    @Autowired
    private IOrderExternalRepository    orderExternalRepository;
    @Autowired
    private IRiskExternalRepository     riskExternalRepository;
    @Autowired
    private IUserSignExternalRepository userSignExternalRepository;
    @Autowired
    private IPlusLiftAmountRepository liftAmountRepository;
    @Autowired
    private IEngineExternalRepository engineExternalRepository;
    @Autowired
    private IMemberPlusInfoRepository memberPlusInfoRepository;

    @Override
    public Integer getDefaultProgramId(Integer channelId, Integer configId) {
        String defaultKey = String.format(RedisConstantPrefix.DEFAULT_PRICE_CONFIG_KEY, channelId,
                configId);
        log.info("获取默认定价配置，key：{}", defaultKey);
        String defaultConfig = redisUtils.get(defaultKey);
        log.info("获取默认定价配置，value：{}", defaultConfig);
        if (StringUtils.isBlank(defaultConfig)) {
            return null;
        }
        Integer programId = JSONObject.parseObject(defaultConfig, PlusDefaultProgramPricePo.class)
                .getProgramId();
        log.info("获取默认定价配置，方案id：{}", programId);
        return programId;
    }

    @Override
    public Integer getDefaultProgramId(Integer channelId, Integer configId,Integer bizSource) {
        String defaultKey = String.format(RedisConstantPrefix.DEFAULT_PRICE_CONFIG_KEY, channelId,
                configId);
        if (bizSource != null && bizSource > 1) {
            defaultKey = String.format(RedisConstantPrefix.DEFAULT_PRICE_CONFIG_KEY_NEW, channelId,
                    configId, bizSource);
        }
        log.info("获取默认定价配置，key：{}", defaultKey);
        String defaultConfig = redisUtils.get(defaultKey);
        log.info("获取默认定价配置，value：{}", defaultConfig);
        if (StringUtils.isBlank(defaultConfig)) {
            return null;
        }
        Integer programId = JSONObject.parseObject(defaultConfig, PlusDefaultProgramPricePo.class)
                .getProgramId();
        log.info("获取默认定价配置，方案id：{}", programId);
        return programId;
    }

    @Override
    public Integer getDiffProgramId(PriceConfigQueryEvent event, PriceConfigContext context) {
        return getDiffPrice(event, context);
    }

    @Override
    public boolean checkGradeMatch(Integer programId, String grade) {
        if (programId != null) {
            PlusLiftAmountPo vo = liftAmountRepository.getByProgramId(programId);
            if (vo == null || !grade.equals(vo.getGrade())) {
                log.info("无提额项或方案已下架或提额等级不一致：{}", programId);
                return false;
            }
            log.info("校验路由的方案提额等级与传入的提额等级一致：{},{}", programId, grade);
            return true;
        }
        log.info("校验路由的方案提额等级是否与传入的提额等级一致，方案id为空");
        return false;
    }

    /**
     * 获取差异化定价
     *
     * @param dto 必须参数，如：用户id、渠道id等
     * @param context 上下文参数，如果上下文中存在对应维度数据，则不需要从外部系统获取
     */
    private Integer getDiffPrice(PriceConfigQueryEvent dto, PriceConfigContext context) {
        log.info("获取差异化定价入参：{}，上下文：{}", JSON.toJSONString(dto),
                JSON.toJSONString(context));
        if (context == null) {
            context = new PriceConfigContext();
        }
        Integer userId = dto.getUserId();
        Integer configId = dto.getConfigId();
        Integer channelId = dto.getChannelId();
        String redisKey = String.format(RedisConstantPrefix.DIFF_PRICE_CONFIG_KEY, channelId,
                configId);
        List<String> cacheList = redisUtils.lRange(redisKey, 0, -1);
        log.info("获取差异化定价配置缓存：{}", JSON.toJSONString(cacheList));
        if (CollectionUtils.isEmpty(cacheList)) {
            log.info("获取差异化定价配置-自动，缓存为空:{}", userId);
            return null;
        }
        if (isQuota(configId) && StringUtils.isBlank(dto.getGrade())) {
            log.info("获取差异化定价配置-自动，提额卡提额等级为空，返回空处理:{}", userId);
            return null;
        }
        // 差异化配置列表
        List<SaveDifferenceProgramPriceEvent> priceList = cacheList.stream()
                .map(e -> JSONObject.parseObject(e, SaveDifferenceProgramPriceEvent.class))
                .collect(Collectors.toList());
        // 获取单个即可，因为多个的配置维度是一致的
        SaveDifferenceProgramPriceEvent price = priceList.get(0);
        BigDecimal availableMinPrice = price.getAvailableMinPrice();
        BigDecimal loanMinPrice = price.getLoanMinPrice();
        BigDecimal promoteMinPrice = price.getPromoteMinPrice();
        BigDecimal promoteAvailableMinPrice = price.getPromoteAvailableMinPrice();
        Integer periods = price.getPeriods();
        String userLevel = price.getUserLevel();
        BigDecimal realityQuotaMinPrice = price.getRealityQuotaMinPrice();
        // ltq 20240716 用户客群差异化营销新增数据维度
        BigDecimal loanRfmMinScore = price.getLoanRfmMinScore();
        BigDecimal plusRfmMinScore = price.getPlusRfmMinScore();
        BigDecimal marketReachMinScore = price.getMarketReachMinScore();
        Integer userLastMinNumber = price.getUserLastMinNumber();
        Boolean memberUser = price.getMemberUser();
        // 请求上下文
        Map<String, Object> requestContext = new HashMap<>();
        // 剩余可借金额
        if (availableMinPrice != null) {
            log.info("开始设置剩余可借额度条件值：{}", userId);
            boolean check = handlerAvailable(context, dto, requestContext);
            if (!check) {
                log.info("开始设置剩余可借额度条件值，设置失败：{}", userId);
                return null;
            }
        }
        // 订单借款金额
        if (loanMinPrice != null) {
            log.info("开始设订单借款金额、借款期数条件值：{}", userId);
            boolean b = handlerLoan(context, dto, requestContext);
            if (!b) {
                log.info("开始设订单借款金额、设置失败：{}", userId);
                return null;
            }
        }
        // 订单分期期数
        if (periods != null) {
            log.info("开始设置订单分期期数、分期期数条件值：{}", userId);
            boolean b = handlerPeriods(context, dto, requestContext);
            if (!b) {
                log.info("开始设置订单分期期数、设置失败：{}", userId);
                return null;
            }
        }
        // 会员可提额度
        if (promoteMinPrice != null) {
            log.info("开始设置会员可提额度条件值：{}", userId);
            boolean b = handlerPromote(context, dto, requestContext);
            if (!b) {
                log.info("开始设置会员可提额度条件值，设置失败：{}", userId);
                return null;
            }
        }
        // 提额后可借金额
        if (promoteAvailableMinPrice != null) {
            log.info("开始设置提额后可借金额条件值：{}", userId);
            boolean b = handlerPromoteAvailable(context, dto, requestContext);
            if (!b) {
                log.info("开始设置提额后可借金额条件值，设置失败：{}", userId);
                return null;
            }
        }
        // 用户等级
        if (StringUtils.isNotBlank(userLevel)) {
            log.info("开始设置用户等级条件值：{}", userId);
            boolean b = handlerUserLevel(context, dto);
            if (!b) {
                log.info("开始设置用户等级条件值，设置失败：{}", userId);
                return null;
            }
        }
        // 实际可提升额度
        if (realityQuotaMinPrice != null) {
            log.info("开始设置实际可提升额度条件值：{}", userId);
            boolean b = handlerRealityQuota(context, dto, requestContext);
            if (!b) {
                log.info("开始设置实际可提升额度条件值，设置失败：{}", userId);
                return null;
            }
        }
        // 20240329 zjf 重提客群用户
        if (price.getResubmitUser() != null) {
            log.info("开始设置重提客群用户条件值：{}", userId);
            handlerResubmitUser(context, dto);
        }
        // 借款RFM模型分
        if (loanRfmMinScore != null) {
            log.info("开始设置借款RFM模型分条件值：{}", userId);
            boolean b = handlerLoanRfmScore(context, dto);
            if (!b) {
                log.info("开始设置借款RFM模型分条件值，设置失败：{}", userId);
                return null;
            }
        }
        // 会员RFM模型分
        if (plusRfmMinScore != null) {
            log.info("开始设置会员RFM模型分条件值：{}", userId);
            boolean b = handlerPlusRfmScore(context, dto);
            if (!b) {
                log.info("开始设置会员RFM模型分条件值，设置失败：{}", userId);
                return null;
            }
        }
        // 营销触达分
        if (marketReachMinScore != null) {
            log.info("开始设置营销触达分条件值：{}", userId);
            boolean b = handlerMarketReachScore(context, dto);
            if (!b) {
                log.info("开始设置营销触达分条件值，设置失败：{}", userId);
                return null;
            }
        }
        // 用户尾号
        if (userLastMinNumber != null) {
            log.info("开始设置用户尾号条件值：{}", userId);
            boolean b = handlerUserLastNumber(context, dto);
            if (!b) {
                log.info("开始设置用户尾号条件值，设置失败：{}", userId);
                return null;
            }
        }
        // 是否会员身份
        if (memberUser != null) {
            log.info("开始设置是否会员身份条件值：{}", userId);
            handlerMemberUser(context, dto);
        }
        return getPriceConfig(priceList, context, dto);
    }

    /**
     * 处理是否会员身份
     */
    private void handlerMemberUser(PriceConfigContext context, PriceConfigQueryEvent dto) {
        Integer userId = dto.getUserId();
        Boolean memberUser = context.getMemberUser();
        if (memberUser != null) {
            log.info("处理是否会员身份，上下文传入的是否会员身份：{},{}", userId, memberUser);
            return;
        }
        log.info("处理是否会员身份-自动，开始填充是否会员身份：{}", userId);
        boolean isMemberUser = !CollectionUtils.isEmpty(
                memberPlusInfoRepository.getByUserAndConfigIds(userId,
                        PlusConstant.MERGE_CARD_LIST));
        context.setMemberUser(isMemberUser);
    }

    /**
     * 处理用户尾号
     */
    private boolean handlerUserLastNumber(PriceConfigContext context, PriceConfigQueryEvent dto) {
        Integer userId = dto.getUserId();
        Integer userLastNumber = context.getUserLastNumber();
        if (userLastNumber != null) {
            log.info("处理用户尾号，上下文传入的用户尾号：{},{}", userId, userLastNumber);
            return true;
        }
        log.info("处理用户尾号-自动，开始填充用户尾号：{}", userId);
        if (userId == null) {
            log.info("处理用户尾号-自动，获取用户尾号为空");
            return false;
        }
        context.setUserLastNumber(userId % 100);
        return true;
    }

    /**
     * 处理营销触达分
     */
    private boolean handlerMarketReachScore(PriceConfigContext context, PriceConfigQueryEvent dto) {
        Integer userId = dto.getUserId();
        BigDecimal marketReachScore = context.getMarketReachScore();
        if (marketReachScore != null) {
            log.info("处理营销触达分，上下文传入的营销触达分：{},{}", userId, marketReachScore);
            return true;
        }
        log.info("处理营销触达分-自动，开始填充营销触达分：{}", userId);
        DistributionMarketEntity result = engineExternalRepository.getDistributionMarketResult(null,
                userId, EngineCodeConstant.SCORE_ENGIN_CODE, null);
        BigDecimal jcMarketReachScore = result.getMarketReachScore();
        if (jcMarketReachScore == null) {
            log.info("处理营销触达分-自动,调用桔策获取营销触达分返回为空");
            return false;
        }
        context.setMarketReachScore(jcMarketReachScore);
        return true;
    }

    /**
     * 处理会员RFM模型分
     */
    private boolean handlerPlusRfmScore(PriceConfigContext context, PriceConfigQueryEvent dto) {
        Integer userId = dto.getUserId();
        BigDecimal plusRfmScore = context.getPlusRfmScore();
        if (plusRfmScore != null) {
            log.info("处理会员RFM模型分，上下文传入的会员RFM模型分：{},{}", userId, plusRfmScore);
            return true;
        }
        log.info("处理会员RFM模型分-自动，开始填充会员RFM模型分：{}", userId);
        DistributionMarketEntity result = engineExternalRepository.getDistributionMarketResult(null,
                userId, EngineCodeConstant.SCORE_ENGIN_CODE, null);
        BigDecimal jcPlusRfmScore = result.getPlusRfmScore();
        if (jcPlusRfmScore == null) {
            log.info("处理会员RFM模型分-自动,调用桔策获取会员RFM模型分返回为空");
            return false;
        }
        context.setPlusRfmScore(jcPlusRfmScore);
        return true;
    }

    /**
     * 处理借款RFM模型分
     */
    private boolean handlerLoanRfmScore(PriceConfigContext context, PriceConfigQueryEvent dto) {
        Integer userId = dto.getUserId();
        BigDecimal loanRfmScore = context.getLoanRfmScore();
        if (loanRfmScore != null) {
            log.info("处理借款RFM模型分，上下文传入的借款RFM模型分：{},{}", userId, loanRfmScore);
            return true;
        }
        log.info("处理借款RFM模型分-自动，开始填充借款RFM模型分：{}", userId);
        DistributionMarketEntity result = engineExternalRepository.getDistributionMarketResult(null,
                userId, EngineCodeConstant.SCORE_ENGIN_CODE, null);
        BigDecimal jcLoanRfmScore = result.getLoanRfmScore();
        if (jcLoanRfmScore == null) {
            log.info("处理借款RFM模型分-自动,调用桔策获取借款RFM模型分返回为空");
            return false;
        }
        context.setLoanRfmScore(jcLoanRfmScore);
        return true;
    }

    /**
     * 处理实际可提升额度
     */
    private boolean handlerRealityQuota(PriceConfigContext context, PriceConfigQueryEvent dto,
            Map<String, Object> requestContext) {
        Integer userId = dto.getUserId();
        if (context.getRealityQuota() != null) {
            log.info("获取差异化定价配置-手动，上下文传入的实际可提升额度：{}，{}", userId,
                    context.getRealityQuota());
            return true;
        }
        RiskFuseEntity riskRaise = getRiskRaise(userId, dto.getChannelId(), dto.getGrade(),
                requestContext);
        if (riskRaise == null || riskRaise.getCanBuy() == null || !riskRaise.getCanBuy()) {
            log.info("获取差异化定价配置-自动-实际可提升额度，获取风控可提额度风控返回不可购买：{}",
                    userId);
            return false;
        }
        if (riskRaise.getRaiseAmount() == null
                || riskRaise.getRaiseAmount().compareTo(BigDecimal.ZERO) == 0) {
            log.info(
                    "获取差异化定价配置-自动-实际可提升额度，获取风控可提额度风控返回可提额度为空：{}",
                    userId);
            return false;
        }
        // 额度试算
        CreditCalculateVO creditCalculateVO = creditCalculateSimulation(userId,
                riskRaise.getRaiseAmount(), dto.getGrade(), requestContext);
        // 额度信息
        CustomerCreditChannelDetailVO account = getCreditAccount(userId, requestContext);
        if (Objects.isNull(account)) {
            log.info("获取差异化定价配置-自动-实际可提升额度，用户额度信息为空:{}", userId);
            return false;
        }
        // 提额后可借额度
        BigDecimal borrowUsableSumAll = creditCalculateVO.getBorrowUsableSumAll();
        // 剩余可借额度
        BigDecimal usableSumAllBorrow = account.getBorrowUsableSumAll() == null ? BigDecimal.ZERO
                : account.getBorrowUsableSumAll();
        // 差值小于500则不能差异化
        BigDecimal creditAmount = borrowUsableSumAll.subtract(usableSumAllBorrow);
        if (creditAmount.compareTo(new BigDecimal("500")) < 0) {
            log.info("获取差异化定价配置-自动，用户实际可提升额度小于500，{}，{}", userId,
                    creditAmount);
            return false;
        }
        context.setRealityQuota(creditAmount);
        log.info("获取差异化定价配置-自动，填充实际可提升额度为：{}，{}", userId,
                context.getRealityQuota());
        return true;
    }

    /**
     * 处理借款金额-501或611状态的借款单
     */
    private boolean handlerLoan(PriceConfigContext context, PriceConfigQueryEvent dto,
            Map<String, Object> requestContext) {
        Integer userId = dto.getUserId();
        if (context.getLoan() != null) {
            log.info("获取差异化定价配置-手动，上下文传入的借款金额：{}，{}", userId,
                    context.getLoan());
            return true;
        }
        // 获取借款金额
        Orders orderDetail = getOrderDetail(userId, requestContext);
        if (orderDetail == null) {
            log.info("获取差异化定价配置-自动-借款金额，查询订单信息为空：{}", userId);
            return false;
        }
        context.setLoan(orderDetail.getMoneyOrder());
        log.info("获取差异化定价配置-自动，填充借款金额为：{}，{}", userId, context.getLoan());
        return true;
    }

    /**
     * 获取501或611状态的借款单
     */
    private Orders getOrderDetail(Integer userId, Map<String, Object> requestContext) {
        /**
         * 请求上下文，501获611状态订单详情key
         */
        String orderKey = "order_detail_611_501";
        Orders orderDetail = (Orders) requestContext.get(orderKey);
        log.info("获取借款订单上下文信息：{}", JSON.toJSONString(orderDetail));
        if (orderDetail == null) {
            orderDetail = orderExternalRepository.lastOneProcessOrder(userId);
            if (orderDetail != null) {
                requestContext.put(orderKey, orderDetail);
                log.info("缓存借款订单上下文信息：{}", JSON.toJSONString(orderDetail));
            }
        }
        return orderDetail;
    }

    /**
     * 是否提额卡
     */
    private boolean isQuota(Integer configId) {
        return JuziPlusEnum.QUOTA_CARD.getCode() == configId
                || JuziPlusEnum.NEW_JUXP_CARD.getCode() == configId
                || JuziPlusEnum.SUCCESS_CARD.getCode() == configId
                || JuziPlusEnum.HYYK_CARD.getCode() == configId
                || JuziPlusEnum.YITONG_CARD.getCode() == configId;
    }

    /**
     * 处理分期期数
     */
    private boolean handlerPeriods(PriceConfigContext context, PriceConfigQueryEvent dto,
            Map<String, Object> requestContext) {
        Integer userId = dto.getUserId();
        if (context.getPeriods() != null) {
            log.info("获取差异化定价配置-手动，上下文传入的分期期数：{}，{}", userId,
                    context.getPeriods());
            return true;
        }
        // 获取借款分期期数
        Orders orderDetail = getOrderDetail(userId, requestContext);
        if (orderDetail == null) {
            log.info("获取差异化定价配置-自动-分期期数，查询订单为空：{}", userId);
            return false;
        }
        OrderSimpleInfoDTO orders = orderExternalRepository.getByOrderSn(orderDetail.getOrderSn());
        if (orders == null) {
            log.info("获取差异化定价配置-自动-分期期数，查询订单期数为空：{}", userId);
            return false;
        }
        context.setPeriods(orders.getPeriodNum());
        log.info("获取差异化定价配置-自动，填充分期期数为：{}，{}", userId, context.getPeriods());
        return true;
    }

    /**
     * 处理用户等级
     */
    private boolean handlerUserLevel(PriceConfigContext context, PriceConfigQueryEvent dto) {
        Integer userId = dto.getUserId();
        if (StringUtils.isNotBlank(context.getUserLevel())) {
            log.info("获取差异化定价配置-手动，上下文传入的用户等级：{}，{}", userId,
                    context.getUserLevel());
            return true;
        }
        log.info("获取差异化定价配置-自动，开始填充用户等级：{}", userId);
        String userLevel = riskExternalRepository.getUserLevel(userId);
        if (StringUtils.isBlank(userLevel)) {
            log.info("获取差异化定价配置-自动，查询用户等级为空：{}", userId);
            return false;
        }
        context.setUserLevel(userLevel);
        log.info("获取差异化定价配置-自动，填充用户等级为：{}，{}", userId, context.getUserLevel());
        return true;
    }

    /**
     * 处理剩余可借额度
     */
    private boolean handlerAvailable(PriceConfigContext context, PriceConfigQueryEvent dto,
            Map<String, Object> requestContext) {
        Integer userId = dto.getUserId();
        if (context.getAvailable() != null) {
            log.info("获取差异化定价配置-手动，上下文传入的剩余可借额度：{}，{}", userId,
                    context.getAvailable());
            return true;
        }
        log.info("获取差异化定价配置-自动，开始填充剩余可借额度：{}", userId);
        CustomerCreditChannelDetailVO account = getCreditAccount(userId, requestContext);
        if (Objects.isNull(account) || Objects.isNull(account.getBorrowUsableSumAll())) {
            log.info("获取差异化定价配置-自动，剩余可借额度为空:{}", userId);
            return false;
        }
        context.setAvailable(account.getBorrowUsableSumAll());
        log.info("获取差异化定价配置-自动，剩余可借额度填充为：{}", context.getAvailable());
        return true;
    }

    /**
     * 获取额度信息
     */
    private CustomerCreditChannelDetailVO getCreditAccount(Integer userId,
            Map<String, Object> requestContext) {
        /**
         * 请求上下文，额度信息key
         */
        String accountKey = "account";
        CustomerCreditChannelDetailVO account = (CustomerCreditChannelDetailVO) requestContext.get(
                accountKey);
        log.info("获取额度信息上下文缓存：{}", JSON.toJSONString(account));
        if (account == null) {
            account = creditExternalRepository.getAccountInfo(userId, false);
            if (account != null) {
                requestContext.put(accountKey, account);
                log.info("设置额度信息上下文：{}", JSON.toJSONString(account));
            }
        }
        return account;
    }

    /**
     * 处理会员提额
     */
    private boolean handlerPromote(PriceConfigContext context, PriceConfigQueryEvent dto,
            Map<String, Object> requestContext) {
        Integer userId = dto.getUserId();
        if (context.getPromote() != null) {
            log.info("获取差异化定价配置-手动，上下文传入的会员可提额度：{}，{}", userId,
                    context.getPromote());
            return false;
        }
        log.info("获取差异化定价配置-自动，开始填充会员可提额度：{}", userId);
        RiskFuseEntity riskRaise = getRiskRaise(userId, dto.getChannelId(), dto.getGrade(),
                requestContext);
        if (riskRaise == null || riskRaise.getCanBuy() == null || !riskRaise.getCanBuy()) {
            log.info("获取差异化定价配置-自动，获取风控可提额度风控返回不可购买：{}", userId);
            return false;
        }
        if (riskRaise.getRaiseAmount() == null
                || riskRaise.getRaiseAmount().compareTo(BigDecimal.ZERO) == 0) {
            log.info("获取差异化定价配置-自动，获取风控可提额度风控返回可提额度为空：{}", userId);
            return false;
        }
        context.setPromote(riskRaise.getRaiseAmount());
        log.info("获取差异化定价配置-自动，填充会员可提额度为：{}，{}", userId, context.getPromote());
        return true;
    }

    /**
     * 获取风控熔断信息
     */
    private RiskFuseEntity getRiskRaise(Integer userId, Integer channelId, String grade,
            Map<String, Object> requestContext) {
        /**
         * 请求上下文，风控熔断提额信息
         */
        String riskKey = "risk";
        RiskFuseEntity riskRaise = (RiskFuseEntity) requestContext.get(riskKey);
        log.info("获取风控熔断信息上下文：{}", JSON.toJSONString(riskRaise));
        if (riskRaise == null) {
            riskRaise = riskExternalRepository.checkFused(userId, channelId, grade);
            if (riskRaise != null) {
                requestContext.put(riskKey, riskRaise);
                log.info("缓存风控熔断信息上下文：{}", JSON.toJSONString(riskRaise));
            }
        }
        return riskRaise;
    }

    /**
     * 处理提额后可借额度
     */
    private boolean handlerPromoteAvailable(PriceConfigContext context, PriceConfigQueryEvent dto,
            Map<String, Object> requestContext) {
        Integer userId = dto.getUserId();
        if (context.getPromoteAvailable() != null) {
            log.info("获取差异化定价配置-手动，上下文传入的提额后可借额度：{}，{}", userId,
                    context.getPromoteAvailable());
            return false;
        }
        log.info("获取差异化定价配置-自动，开始填充提额后可借额度：{}，{}", userId, dto.getGrade());
        RiskFuseEntity riskRaise = getRiskRaise(userId, dto.getChannelId(), dto.getGrade(),
                requestContext);
        if (riskRaise == null || riskRaise.getCanBuy() == null || !riskRaise.getCanBuy()) {
            log.info("获取差异化定价配置-自动-提额后可借额度，获取风控可提额度风控返回不可购买：{}",
                    userId);
            return false;
        }
        if (riskRaise.getRaiseAmount() == null
                || riskRaise.getRaiseAmount().compareTo(BigDecimal.ZERO) == 0) {
            log.info(
                    "获取差异化定价配置-自动-提额后可借额度，获取风控可提额度风控返回可提额度为空：{}",
                    userId);
            return false;
        }
        CreditCalculateVO creditCalculateVO = creditCalculateSimulation(userId,
                riskRaise.getRaiseAmount(), dto.getGrade(), requestContext);
        if (creditCalculateVO == null) {
            log.info("获取差异化定价配置-自动，开始填充提额后可借额度，试算额度为空：{}", userId);
            return false;
        }
        context.setPromoteAvailable(creditCalculateVO.getBorrowUsableSumAll());
        log.info("获取差异化定价配置-自动，填充提额后可借额度为：{}，{}", userId,
                context.getPromoteAvailable());
        return true;
    }

    /**
     * 获取额度系统试算结果
     */
    private CreditCalculateVO creditCalculateSimulation(Integer userId, BigDecimal raiseAmount,
            String grade, Map<String, Object> requestContext) {
        /**
         * 请求上下文，额度试算结果
         */
        String calculateKey = "calculate";
        CreditCalculateVO creditCalculateVO = (CreditCalculateVO) requestContext.get(calculateKey);
        log.info("获取额度试算结果上下文：{}", JSON.toJSONString(creditCalculateVO));
        if (creditCalculateVO == null) {
            CreditCalculateRequest request = new CreditCalculateRequest();
            request.setCustomerId(userId);
            request.setBizType(CommonConstant.THREE);
            request.setQuota(raiseAmount);
            String newGrade = NewGradeEnum.getNewGrade(grade);
            request.setItemCode(newGrade);
            creditCalculateVO = creditExternalRepository.creditCalculateSimulation(request);
            if (creditCalculateVO != null) {
                requestContext.put(calculateKey, creditCalculateVO);
                log.info("缓存额度试算结果上下文：{}", JSON.toJSONString(creditCalculateVO));
            }
        }
        return creditCalculateVO;
    }

    /**
     * 处理重提客群用户
     */
    private void handlerResubmitUser(PriceConfigContext context, PriceConfigQueryEvent dto) {
        Integer userId = dto.getUserId();
        if (context.getResubmitUser() != null) {
            log.info("获取重提客群用户配置-手动，上下文传入的重提客群用户：{}，{}", userId,
                    context.getResubmitUser());
            return;
        }
        log.info("获取重提客群用户配置-自动，开始填充重提客群用户配置：{}", userId);
        // 请求标签系统获取用户标签
        List<UserSignInfoEntity> signInfoList = userSignExternalRepository.getUserBrowseVariable(
                userId, Collections.singletonList(CommonConstant.RESUBMIT_USER_CODE));
        // 非重提客群：1. 变量值返回0 2.code =200 && 变量值返回未空
        if (!CollectionUtils.isEmpty(signInfoList)) {
            UserSignInfoEntity userSignInfo = signInfoList.stream()
                    .filter(u -> CommonConstant.RESUBMIT_USER_CODE.equals(u.getVariableCode()))
                    .findFirst().orElse(null);
            if (userSignInfo == null || StringUtils.isBlank(userSignInfo.getVariableValue())
                    || CommonConstant.STRING_ZERO.equals(userSignInfo.getVariableValue())) {
                log.info(
                        "获取重提客群用户配置-自动，标签系统返回变量值为空或0，按非重提客群用户处理：{}",
                        userId);
                context.setResubmitUser(false);
                return;
            }
        }
        context.setResubmitUser(true);
        log.info(
                "获取重提客群用户配置-自动，标签系统返回code非200或data为空或调用超时或返回是重提客群，按重提客群用户处理：{}",
                userId);
    }

    /**
     * 获取定价配置
     *
     * @return 方案id
     */
    private Integer getPriceConfig(List<SaveDifferenceProgramPriceEvent> priceList,
            PriceConfigContext context, PriceConfigQueryEvent dto) {
        log.info("获取差异化定价配置，缓存数据：{}", JSONObject.toJSONString(priceList));
        if (CollectionUtils.isEmpty(priceList)) {
            log.info("获取差异化定价配置，缓存为空：{}，{}，{}", dto.getUserId(), dto.getChannelId(),
                    dto.getConfigId());
            return null;
        }
        log.info("获取定价配置入参：{}", JSONObject.toJSONString(context));
        BigDecimal available = context.getAvailable();
        BigDecimal promote = context.getPromote();
        BigDecimal promoteAvailable = context.getPromoteAvailable();
        BigDecimal loan = context.getLoan();
        String userLevel = context.getUserLevel();
        Integer periods = context.getPeriods();
        BigDecimal realityQuota = context.getRealityQuota();
        Boolean resubmitUser = context.getResubmitUser();
        // ltq 20240716 用户客群差异化营销新增数据维度
        BigDecimal loanRfmScore = context.getLoanRfmScore();
        BigDecimal plusRfmScore = context.getPlusRfmScore();
        BigDecimal marketReachScore = context.getMarketReachScore();
        Integer userLastNumber = context.getUserLastNumber();
        Boolean memberUser = context.getMemberUser();
        SaveDifferenceProgramPriceEvent priceConfig = priceList.stream().filter(e -> {
            // 传入数据为空 或 未配置对应变量维度，则不需要处理，下同
            log.info("匹配剩余可借额度-传入字段值：{}，变量维度配置：{}，{}", available,
                    e.getAvailableMinPrice(), e.getAvailableMaxPrice());
            if (available == null || e.getAvailableMinPrice() == null
                    || e.getAvailableMaxPrice() == null) {
                return true;
            }
            return available.compareTo(e.getAvailableMinPrice()) >= 0
                    && available.compareTo(e.getAvailableMaxPrice()) < 0;
        }).filter(e -> {
            log.info("匹配会员可提额度-传入字段值：{}，变量维度配置：{}，{}", promote,
                    e.getPromoteMinPrice(), e.getPromoteMaxPrice());
            if (promote == null || e.getPromoteMinPrice() == null
                    || e.getPromoteMaxPrice() == null) {
                return true;
            }
            return promote.compareTo(e.getPromoteMinPrice()) >= 0
                    && promote.compareTo(e.getPromoteMaxPrice()) < 0;
        }).filter(e -> {
            log.info("匹配提额后可借额度-传入字段值：{}，变量维度配置：{}，{}", promoteAvailable,
                    e.getPromoteAvailableMinPrice(), e.getPromoteAvailableMaxPrice());
            if (promoteAvailable == null || e.getPromoteAvailableMinPrice() == null
                    || e.getPromoteAvailableMaxPrice() == null) {
                return true;
            }
            return promoteAvailable.compareTo(e.getPromoteAvailableMinPrice()) >= 0
                    && promoteAvailable.compareTo(e.getPromoteAvailableMaxPrice()) < 0;
        }).filter(e -> {
            log.info("匹配借款金额-传入字段值：{}，变量维度配置：{}，{}", loan, e.getLoanMinPrice(),
                    e.getLoanMaxPrice());
            if (loan == null || e.getLoanMinPrice() == null || e.getLoanMaxPrice() == null) {
                return true;
            }
            return loan.compareTo(e.getLoanMinPrice()) >= 0
                    && loan.compareTo(e.getLoanMaxPrice()) < 0;
        }).filter(e -> {
            log.info("匹配分期期数-传入字段值：{}，变量维度配置：{}", periods, e.getPeriods());
            if (periods == null || e.getPeriods() == null) {
                return true;
            }
            return periods.equals(e.getPeriods());
        }).filter(e -> {
            log.info("匹配用户等级-传入字段值：{}，变量维度配置：{}", userLevel, e.getUserLevel());
            if (StringUtils.isBlank(userLevel) || StringUtils.isBlank(e.getUserLevel())) {
                return true;
            }
            return StringUtils.equals(userLevel, e.getUserLevel());
        }).filter(e -> {
            log.info("匹配实际可提升额度-传入字段值：{}，变量维度配置：{}，{}", realityQuota,
                    e.getRealityQuotaMinPrice(), e.getRealityQuotaMaxPrice());
            if (realityQuota == null || e.getRealityQuotaMinPrice() == null
                    || e.getRealityQuotaMaxPrice() == null) {
                return true;
            }
            return realityQuota.compareTo(e.getRealityQuotaMinPrice()) >= 0
                    && realityQuota.compareTo(e.getRealityQuotaMaxPrice()) < 0;
        }).filter(e -> {
            log.info("匹配是否重提客群用户-传入字段值：{}，变量维度配置：{}", resubmitUser,
                    e.getResubmitUser());
            // 20240329 zjf 增加是否重提客群维度
            if (resubmitUser == null || e.getResubmitUser() == null) {
                return true;
            }
            return resubmitUser.equals(e.getResubmitUser());
        }).filter(e -> {
            log.info("匹配提额等级-传入字段值：{}，变量维度配置：{}，卡类型：{}", dto.getGrade(),
                    e.getGrade(), dto.getConfigId());
            // 桔享卡才匹配提额等级
            if (StringUtils.isBlank(dto.getGrade())
                    || dto.getConfigId() != JuziPlusEnum.NEW_JUXP_CARD.getCode()) {
                return true;
            }
            return StringUtils.equals(dto.getGrade(), e.getGrade());
        }).filter(e -> {
            // ltq 20240716 增加借款RFM模型分维度
            log.info("匹配借款RFM模型分-传入字段值：{}，变量维度配置：{}，{}", loanRfmScore,
                    e.getLoanRfmMinScore(), e.getLoanRfmMaxScore());
            if (loanRfmScore == null || e.getLoanRfmMinScore() == null
                    || e.getLoanRfmMaxScore() == null) {
                return true;
            }
            return loanRfmScore.compareTo(e.getLoanRfmMinScore()) >= 0
                    && loanRfmScore.compareTo(e.getLoanRfmMaxScore()) < 0;
        }).filter(e -> {
            // ltq 20240716 增加会员RFM模型分维度
            log.info("匹配会员RFM模型分-传入字段值：{}，变量维度配置：{}，{}", plusRfmScore,
                    e.getPlusRfmMinScore(), e.getPlusRfmMaxScore());
            if (plusRfmScore == null || e.getPlusRfmMinScore() == null
                    || e.getPlusRfmMaxScore() == null) {
                return true;
            }
            return plusRfmScore.compareTo(e.getPlusRfmMinScore()) >= 0
                    && plusRfmScore.compareTo(e.getPlusRfmMaxScore()) < 0;
        }).filter(e -> {
            // ltq 20240716 增加营销触达分维度
            log.info("匹配营销触达分-传入字段值：{}，变量维度配置：{}，{}", marketReachScore,
                    e.getMarketReachMinScore(), e.getMarketReachMaxScore());
            if (marketReachScore == null || e.getMarketReachMinScore() == null
                    || e.getMarketReachMaxScore() == null) {
                return true;
            }
            return marketReachScore.compareTo(e.getMarketReachMinScore()) >= 0
                    && marketReachScore.compareTo(e.getMarketReachMaxScore()) < 0;
        }).filter(e -> {
            // ltq 20240716 增加用户尾号维度
            log.info("匹配用户尾号-传入字段值：{}，变量维度配置：{}，{}", userLastNumber,
                    e.getUserLastMinNumber(), e.getUserLastMaxNumber());
            if (userLastNumber == null || e.getUserLastMinNumber() == null
                    || e.getUserLastMaxNumber() == null) {
                return true;
            }
            return userLastNumber >= e.getUserLastMinNumber()
                    && userLastNumber < e.getUserLastMaxNumber();
        }).filter(e -> {
            // ltq 20240716 增加是否会员身份维度
            log.info("匹配是否会员身份-传入字段值：{}，变量维度配置：{}", memberUser,
                    e.getMemberUser());
            if (memberUser == null || e.getMemberUser() == null) {
                return true;
            }
            return memberUser.equals(e.getMemberUser());
        }).findFirst().orElse(null);
        log.info("获取差异化定价配置结果：{}", JSONObject.toJSONString(priceConfig));
        return priceConfig != null ? priceConfig.getProgramId() : null;
    }
}
