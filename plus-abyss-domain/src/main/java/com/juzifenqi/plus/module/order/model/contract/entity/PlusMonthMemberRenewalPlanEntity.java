package com.juzifenqi.plus.module.order.model.contract.entity;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 会员月卡续费计划实体
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
@Data
public class PlusMonthMemberRenewalPlanEntity {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 渠道
     */
    private Integer channelId;

    /**
     * 会员订单号
     */
    private String orderSn;

    /**
     * 月卡编号
     */
    private String monthNo;

    /**
     * 总期次
     */
    private Integer monthPeriod;

    /**
     * 当前期次
     */
    private Integer currentPeriod;

    /**
     * 月卡金额
     */
    private BigDecimal monthOrderAmount;

    /**
     * 方案ID
     */
    private Integer programId;

    /**
     * 计划状态 1已生成 2待生成 3生成中 4生成失败 5作废
     */
    private Integer planState;

    /**
     * 收款主体id
     */
    private Integer supplierId;

    /**
     * 计划生成时间
     */
    private Date planTime;

    /**
     * 实际生成时间
     */
    private Date actualPlanTime;

    /**
     * 计划备注(失败原因)
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
