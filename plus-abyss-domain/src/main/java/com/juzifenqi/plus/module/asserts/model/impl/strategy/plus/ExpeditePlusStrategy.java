package com.juzifenqi.plus.module.asserts.model.impl.strategy.plus;

import com.alibaba.fastjson.JSON;
import com.juzifenqi.plus.enums.JuziPlusEnum;
import com.juzifenqi.plus.module.asserts.model.contract.IMemberPlusExclusiveRepository;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusInfoDetailEntity;
import com.juzifenqi.plus.module.asserts.model.event.LoanSucProfitsEvent;
import com.juzifenqi.plus.module.asserts.repository.po.PlusUseProfitSuPo;
import com.juzifenqi.plus.module.common.repository.external.acl.AuthExternalRepositoryAcl;
import com.jzfq.auth.core.entity.AuthApproval;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 加速卡策略
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/13 15:40
 */
@Slf4j
@Component
public class ExpeditePlusStrategy extends AbstractStrategy {

    @Autowired
    private IMemberPlusExclusiveRepository exclusiveRepository;
    @Autowired
    private AuthExternalRepositoryAcl authExternalRepositoryAcl;

    @Override
    public void doLoanSucProfit(LoanSucProfitsEvent event) {
        log.info("加速卡放款成功处理专属权益信息开始：{}", JSON.toJSONString(event));
        Integer userId = event.getUserId();
        Integer channelId = event.getChannelId();
        String loanOrderSn = event.getLoanOrderSn();
//        List<Integer> configIds =
//                Objects.equals(JuziPlusEnum.EXPEDITE_CARD.getCode(), event.getConfigId())
//                        ? Collections.singletonList(JuziPlusEnum.EXPEDITE_CARD.getCode())
//                        : Arrays.asList(JuziPlusEnum.NEW_JUXP_CARD.getCode(),
//                                JuziPlusEnum.QUOTA_CARD.getCode(), JuziPlusEnum.YITONG_CARD.getCode());
        List<Integer> configIds = Arrays.asList(JuziPlusEnum.EXPEDITE_CARD.getCode(), JuziPlusEnum.NEW_JUXP_CARD.getCode(),
                JuziPlusEnum.QUOTA_CARD.getCode(), JuziPlusEnum.YITONG_CARD.getCode(), JuziPlusEnum.HYYK_CARD.getCode());
        MemberPlusInfoDetailEntity detail;

        if (configProperties.authChannel.contains(channelId.toString())) {
            AuthApproval authState = authExternalRepositoryAcl.getAuthState(userId, channelId);
            detail = plusQueryModel.getMemberPlusAfterAuthTime(userId, channelId, configIds, authState.getSuccessTime());
        } else {
            detail = plusQueryModel.getUserFirstInfoInLastWeek(userId,
                    channelId, configIds);
        }
        if (detail == null) {
            log.info("加速卡放款成功处理专属权益非会员身份：{}", userId);
            return;
        }
        String orderSn = detail.getOrderSn();
        PlusUseProfitSuPo po = new PlusUseProfitSuPo();
        po.setUserId(userId);
        po.setChannelId(event.getChannelId());
        po.setOrderSn(loanOrderSn);
        po.setOrderStatus(event.getOrderStatus());
        po.setPlusOrderSn(orderSn);
        po.setConfigId(detail.getConfigId());
        // 判断卡生效时间在借款单的先还是后
        Date effectiveTime = detail.getOrderPayTime() != null ? detail.getOrderPayTime() : detail.getCreateTime();
        po.setBuyCardType(effectiveTime.getTime() >= event.getLoanOrderCreateTime()
                ? PlusUseProfitSuPo.BUY_CARD_TYPE_AFTER : PlusUseProfitSuPo.BUY_CARD_TYPE_BEFORE);
        exclusiveRepository.savePlusUseProfitSuPo(po);
        log.info("加速卡放款成功处理专属权益信息完成：{},{},{}", loanOrderSn, userId, orderSn);
    }
}
