package com.juzifenqi.plus.module.asserts.model.impl.strategy.profits;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.groot.utils.core.date.LocalDateTimeUtils;
import com.juzifenqi.coupon.entity.enums.CouponCategoryEnum;
import com.juzifenqi.coupon.entity.shop.JuziCouponUser;
import com.juzifenqi.coupon.entity.shop.vo.CouponViewVo;
import com.juzifenqi.enumeration.OrderStateEnum;
import com.juzifenqi.order.dao.entity.OrdersBase;
import com.juzifenqi.plus.constants.CommonConstant;
import com.juzifenqi.plus.constants.PlusConstant;
import com.juzifenqi.plus.dto.req.MemberPlusProfitSendPlanQueryReq;
import com.juzifenqi.plus.enums.NewGradeEnum;
import com.juzifenqi.plus.enums.PlusSendPlanStatusEnum;
import com.juzifenqi.plus.module.asserts.model.MemberPlusSendPlanModel;
import com.juzifenqi.plus.module.asserts.model.MemberProfitsQueryModel;
import com.juzifenqi.plus.module.asserts.model.PlusMemberCouponModel;
import com.juzifenqi.plus.module.asserts.model.contract.*;
import com.juzifenqi.plus.module.asserts.model.contract.entity.ExpireMemberPlusInfoDetailEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberIncQuotaEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusInfoDetailEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusSendPlanEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.coupon.CouponAdminEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.coupon.JuziCouponEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.coupon.MemberCouponCommonEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.coupon.MemberCouponInfoEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.coupon.MemberPlusCouponRecordEntity;
import com.juzifenqi.plus.module.asserts.model.converter.IProfitsHandlerConverter;
import com.juzifenqi.plus.module.asserts.model.entity.profit.ProfitModelBasicDetailEntity;
import com.juzifenqi.plus.module.asserts.model.event.HandleProfitsEvent;
import com.juzifenqi.plus.module.asserts.model.event.QueryProfitUsedEvent;
import com.juzifenqi.plus.module.asserts.model.event.model.HandleCheckProfitDataEvent;
import com.juzifenqi.plus.module.asserts.model.event.model.HandleProfitLandEvent;
import com.juzifenqi.plus.module.asserts.model.event.model.HandleProfitQueryEvent;
import com.juzifenqi.plus.module.asserts.model.event.model.HandleResetProfitEvent;
import com.juzifenqi.plus.module.asserts.repository.po.PlusUseProfitSuPo;
import com.juzifenqi.plus.module.common.IAuthExternalRepository;
import com.juzifenqi.plus.module.common.IExternalCouponRepository;
import com.juzifenqi.plus.module.order.model.PlusOrderSnapshtoQueryModel;
import com.juzifenqi.plus.module.order.model.contract.external.IOrderExternalRepository;
import com.juzifenqi.plus.module.program.model.IPlusProfitQueryModel;
import com.juzifenqi.plus.module.program.model.IPlusProgramQueryModel;
import com.juzifenqi.plus.module.program.model.contract.IPlusCouponIndexRepository;
import com.juzifenqi.plus.module.program.model.contract.IPlusProgramCouponRepository;
import com.juzifenqi.plus.module.program.model.contract.entity.PlusCouponIndexEntity;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramRejectionPo;
import com.juzifenqi.product.entity.Product;
import com.juzishuke.credit.enums.TempTypeEnum;
import com.juzishuke.credit.vo.CustomerCreditChannelDetailVO;
import com.juzishuke.credit.vo.TempCreditDetailDTO;
import com.juzishuke.credit.vo.TempCreditItemDetailDTO;
import com.juzishuke.framework.common.util.DateUtil;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 权益策略
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/13 14:04
 */
@Slf4j
@Component
public abstract class ProfitsAbstractHandler {

    protected final IProfitsHandlerConverter converter = IProfitsHandlerConverter.instance;

    @Autowired
    protected IExternalCouponRepository externalCouponRepository;
    @Autowired
    protected IMemberPlusCouponRepository plusCouponRepository;
    @Autowired
    protected PlusMemberCouponModel plusMemberCouponModel;
    @Autowired
    private IMemberPlusInfoRepository memberPlusInfoRepository;
    @Autowired
    private IOrderExternalRepository orderExternalRepository;
    @Autowired
    private IMemberPlusInfoDetailRepository plusInfoDetailRepository;
    @Autowired
    protected MemberPlusSendPlanModel sendPlanModel;
    @Autowired
    protected IPlusProgramQueryModel programQueryModel;
    @Autowired
    protected IPlusCouponIndexRepository couponIndexRepository;
    @Autowired
    protected IPlusProfitQueryModel profitQueryModel;
    @Autowired
    protected IMemberPlusSendPlanRepository memberPlusSendPlanRepository;
    @Autowired
    protected IPlusProgramCouponRepository programCouponRepository;
    @Autowired
    protected IAuthExternalRepository authExternalRepository;
    @Autowired
    protected IExternalCouponRepository couponRepository;
    @Autowired
    private MemberProfitsQueryModel memberProfitsQueryModel;
    @Autowired
    private IMemberPlusExclusiveRepository memberPlusExclusiveRepository;

    /**
     * 方案生效时，检查权益数据是否配置
     */
    abstract boolean checkProfitData(HandleCheckProfitDataEvent event);

    /**
     * 会员取消-挪动订单周期-重新生成权益发放计划、优惠券领取周期
     */
    protected void doRestProfits(HandleResetProfitEvent event) {

    }

    /**
     * 获取权益信息领取、使用情况数据
     */
    protected Map<String, Object> getProfitUsedInfo(QueryProfitUsedEvent event) {
        return null;
    }

    /**
     * 获取失效会员权益信息领取、使用情况数据
     */
    protected Map<String, Object> getProfitUsedInfoExpire(QueryProfitUsedEvent event) {
        return null;
    }

    /**
     * 商品/借款单信用支付成功处理权益
     */
    public void doCreditPayProfits(HandleProfitsEvent event) {

    }

    /**
     * 商品/借款单取消成功处理权益
     */
    public void doCancelProfits(HandleProfitsEvent event) {

    }

    /**
     * 商品单下单成功处理权益
     */
    public void doUseProfits(HandleProfitsEvent event) {

    }

    /**
     * 商品单收货处理权益
     */
    public void doOrderReceiveProfits(HandleProfitsEvent event) {

    }

    /**
     * 原方案落地页权益信息
     */
    abstract public Map<String, Object> getProfitForOpen(HandleProfitLandEvent vo);

    /**
     * 新落地页策略
     */
    public Map<String, Object> getProfitForOpenNew(HandleProfitLandEvent event) {
        return getProfitForOpen(event);
    }

    /**
     * 新用户会员详情页权益基本信息
     */
    public ProfitModelBasicDetailEntity getProfitBasicInfo(HandleProfitQueryEvent event) {
        //        return profitQueryModel.getProfitBasicInfo(event);
        return memberProfitsQueryModel.getProfitBasicInfo(event);
    }

    /**
     * 原用户会员详情页权益信息
     */
    abstract public Map<String, Object> getProfitForDetailOld(HandleProfitQueryEvent event);

    /**
     * 查看详情-优惠券、还款优惠处理
     */
    protected void setCommonCouponDetail(List<MemberCouponCommonEntity> couponCommonEntityList, List<MemberCouponInfoEntity> couponInfoEntityList) {
        log.info("查看详情-优惠券/还款优惠处理 couponCommonEntityList={}", JSONObject.toJSONString(couponCommonEntityList));
        Map<Integer, JuziCouponEntity> couponMap = new HashMap<>(couponCommonEntityList.size());
        //设置优惠券领取信息
        for (MemberCouponCommonEntity couponCommonEntity : couponCommonEntityList) {
            JuziCouponEntity coupon = couponMap.get(couponCommonEntity.getCouponId());
            if (coupon == null) {
                coupon = externalCouponRepository.getByCouponId(couponCommonEntity.getCouponId());
                if (coupon == null) {
                    continue;
                }
                couponMap.put(couponCommonEntity.getCouponId(), coupon);
            }
            JuziCouponUser couponUser = null;
            if (Objects.nonNull(couponCommonEntity.getCouponUserId()) || Objects.nonNull(couponCommonEntity.getCouponNos())) {
                log.info("用户优惠券ID={}", couponCommonEntity.getCouponUserId());
                //已使用的优惠券和还款券
                couponUser = externalCouponRepository.getCouponUserByIdOrCouponNo(couponCommonEntity.getCouponUserId(), couponCommonEntity.getCouponNos());
            }
            log.info("couponUser={}", JSONObject.toJSONString(couponUser));
            MemberCouponInfoEntity couponInfoEntity = setCouponPlus(couponUser, coupon);
            couponInfoEntity.setCycleTime(couponCommonEntity.getUpdateTime());
            couponInfoEntity.setReachStatus(0);
            if (couponCommonEntity.getReachStatus() == 1) {
                couponInfoEntity.setReachStatus(1);
            }
            couponInfoEntityList.add(couponInfoEntity);
        }
        log.info("查看详情-优惠券/还款优惠处理 返回数据={}", JSON.toJSONString(couponInfoEntityList));
    }

    /**
     * 查看详情-优惠券、还款优惠处理
     */
    protected MemberCouponInfoEntity setCouponPlus(JuziCouponUser couponUser, JuziCouponEntity coupon) {
        log.info("查看详情-处理优惠券 couponUser={},coupon={}", JSONObject.toJSONString(couponUser), JSONObject.toJSONString(coupon));
        MemberCouponInfoEntity couponInfoEntity = new MemberCouponInfoEntity();
        couponInfoEntity.setCouponId(coupon.getCouponId());
        couponInfoEntity.setTitle(coupon.getCouponName());
        couponInfoEntity.setCouponCategory(coupon.getCouponCategory());
        couponInfoEntity.setPeriod(coupon.getPeriod());
        setPrice(coupon, couponInfoEntity);
        if (Objects.nonNull(couponUser)) {
            coupon.setUseEndTime(couponUser.getUseEndTime());
            coupon.setUseStartTime(couponUser.getUseStartTime());
            //设置单个优惠券是否可用设置状态 CanUse 0已使用 1未使用
            if (couponUser.getCanUse() != null && couponUser.getCanUse() == 0) {
                //已使用
                couponInfoEntity.setStatus(2);
                couponInfoEntity.setOrderSn(couponUser.getOrderSn());
            } else {
                //去使用
                couponInfoEntity.setStatus(1);
                Date nowDate = new Date();
                if (couponUser.getUseEndTime() != null && nowDate.after(couponUser.getUseEndTime())) {
                    //已过期
                    couponInfoEntity.setStatus(3);
                }
            }
        } else {
            //未发放，同时未使用
            couponInfoEntity.setStatus(1);
        }
        return couponInfoEntity;
    }

    /**
     * 优惠券设置
     */
    protected void setPrice(JuziCouponEntity coupon, MemberCouponInfoEntity couponInfoEntity) {
        // 0-满减优惠券，1-折扣优惠券，2、免息优惠券，3、还款优惠券、4、息费折扣券
        //优惠券类型
        couponInfoEntity.setLable(coupon.getCouponCategory());
        couponInfoEntity.setPrice(getPrice(coupon));
        couponInfoEntity.setTitle(coupon.getCouponName());
        if (coupon.getMinAmount() != null) {
            couponInfoEntity.setMinAmount(subZeroAndDot(coupon.getMinAmount().toString()));
        }
        if (coupon.getMaxAmount() != null) {
            couponInfoEntity.setMaxAmount(subZeroAndDot(coupon.getMaxAmount().toString()));
        }
    }

    /**
     * 获取金额
     */
    private String getPrice(JuziCouponEntity coupon) {
        if (coupon.getCouponCategory() == 0) {
            return subZeroAndDot(String.valueOf(coupon.getCouponValue()));
            //折扣优惠券
        } else if (coupon.getCouponCategory() == 2) {
            //几折
            return subZeroAndDot(String.valueOf(coupon.getDiscountCoupon()));
            //还款券
        } else if (coupon.getCouponCategory() == 3) {
            return subZeroAndDot(String.valueOf(coupon.getRepaymentCoupon()));
            //息费折扣
        } else if (coupon.getCouponCategory() == 4) {
            //几折
            return subZeroAndDot(String.valueOf(coupon.getDiscountCoupon()));
            //免息券
        } else {
            //几期免息
            return coupon.getInterestFreeCoupon();
        }
    }

    /**
     * 获取用户最新订单信息(包括支付成功和支付成功后取消)
     */
    protected String subZeroAndDot(String s) {
        if (StringUtils.isBlank(s)) {
            return "";
        }
        if (s.indexOf(".") > 0) {
            //去掉多余的0
            s = s.replaceAll("0+?$", "");
            //如最后一位是.则去掉
            s = s.replaceAll("[.]$", "");
        }
        return s;
    }

    /**
     * 浩瀚后台-用户会员列表-详情页面根据couponViewVo设置优惠券类型和金额
     */
    protected void setPriceDetail(CouponAdminEntity coupon, MemberCouponInfoEntity paidMemberCouponVo) {
        // 0-满减优惠券，1-折扣优惠券，2、免息优惠券，3、还款优惠券、4、息费折扣券
        //优惠券类型
        paidMemberCouponVo.setLable(coupon.getCouponCategory());
        if (!StringUtils.isEmpty(coupon.getPrice())) {
            paidMemberCouponVo.setPrice(coupon.getPrice());
        } else {
            //满减券
            if (coupon.getCouponCategory() == 0) {
                paidMemberCouponVo.setPrice(subZeroAndDot(String.valueOf(coupon.getCouponValue())));
                //折扣优惠券
            } else if (coupon.getCouponCategory() == 2) {
                //几折
                paidMemberCouponVo.setPrice(subZeroAndDot(String.valueOf(coupon.getDiscountCoupon())));
                //还款券
            } else if (coupon.getCouponCategory() == 3) {
                paidMemberCouponVo.setPrice(subZeroAndDot(String.valueOf(coupon.getRepaymentCoupon())));
                //息费折扣
            } else if (coupon.getCouponCategory() == 4) {
                //几折
                paidMemberCouponVo.setPrice(subZeroAndDot(String.valueOf(coupon.getDiscountCoupon())));
                //免息券
            } else {
                //几期免息
                paidMemberCouponVo.setPrice(coupon.getInterestFreeCoupon());
            }
        }
        if (coupon.getMinAmount() != null) {
            paidMemberCouponVo.setMinAmount(subZeroAndDot(coupon.getMinAmount()));
        }
        if (coupon.getMaxAmount() != null) {
            paidMemberCouponVo.setMaxAmount(subZeroAndDot(coupon.getMaxAmount()));
        }
    }

    /**
     * 浩瀚后台-用户会员列表-详情页面组装会员详页月享-品牌专区-开卡礼
     */
    protected void transformDetail(List<CouponAdminEntity> fromList, List<MemberCouponInfoEntity> toList) {
        log.info("会员详情页月享品牌开卡礼数组转换 fromList={}", JSONObject.toJSONString(fromList));
        for (CouponAdminEntity couponViewVo : fromList) {
            MemberCouponInfoEntity memberCouponVo = converter.toMemberCouponInfoEntity(couponViewVo);
            String useStartTime = couponViewVo.getUseStartTime();
            String useEndTime = couponViewVo.getUseEndTime();
            if (!StringUtils.isEmpty(useStartTime)) {
                memberCouponVo.setStartTime(LocalDateTimeUtils.formatDateStrToHh(useStartTime));
            }
            if (!StringUtils.isEmpty(useEndTime)) {
                memberCouponVo.setEndTime(LocalDateTimeUtils.formatDateStrToHh(useEndTime));
            }
            log.info("--------------------memberCoupon:{},couponView：{}", memberCouponVo, couponViewVo);
            memberCouponVo.setCouponUserId(couponViewVo.getId());
            setPriceDetail(couponViewVo, memberCouponVo);
            //根据时间、是否可用设置状态 CanUse 0已使用 1未使用
            if (couponViewVo.getCanUse() != null && couponViewVo.getCanUse() == 0) {
                //已使用
                memberCouponVo.setStatus(2);
            } else {
                //未使用
                memberCouponVo.setStatus(1);
                Date nowDate = new Date();
                String endTime = couponViewVo.getUseEndTime();
                if (endTime != null) {
                    Date endDate = LocalDateTimeUtils.parseStringToDate(endTime, LocalDateTimeUtils.DATE_FORMAT_FULL);
                    if (nowDate.after(endDate)) {
                        //已过期
                        memberCouponVo.setStatus(3);
                    }
                }
            }
            toList.add(memberCouponVo);
        }
        log.info("会员详情页开卡礼或月享数组转换结束 toList={}", JSONObject.toJSONString(toList));
    }

    /**
     * 处理数据
     */
    protected void getCommonCouponResult(List<MemberCouponCommonEntity> commonEntityList, Map<String, Object> map) {
        List<MemberCouponInfoEntity> couponInfoEntityList = new ArrayList<>();
        setCommonCouponDetail(commonEntityList, couponInfoEntityList);
        map.put("isReceive", commonEntityList.stream().anyMatch(e -> e.getReceiveStatus() == 1 || e.getReceiveStatus() == 2));
        map.put("couponList", couponInfoEntityList);
    }

    /**
     * 更快放款/更快审核-权益使用处理
     */
    protected Map<String, Object> getGkProfitUsedInfo(QueryProfitUsedEvent event) {
        log.info("正常会员-查看详情-更快放款/更快审核入参,req={}", JSON.toJSONString(event));
        Map<String, Object> map = new HashMap<>(2);
        map.put("isUse", false);
        MemberPlusInfoDetailEntity detailEntity = memberPlusInfoRepository.getMemberPlusInfoDetail(event.getUserId(), event.getOrderSn());
        if (detailEntity == null) {
            log.info("获取更快放款/更快审核权益使用情况，未获取用户会员信息：{}", event.getUserId());
            return map;
        }
        // ltq 20240705 更快审核、快速放款权益关联借款订单逻辑优化
        if (!PlusConstant.FASTER_CARD_LIST.contains(event.getConfigId())) {
            log.info("正常会员-获取更快放款/更快审核权益使用情况，非固额卡、加速卡、桔享卡、降息卡、重提卡");
            return map;
        }

        //设置会员订单权益使用状态
        wrapPlusProfitUsed(event, map);

        log.info("正常会员-查看详情-更快放款/更快审核,result={}", JSON.toJSONString(map));
        return map;
    }

    /**
     * 过期会员-更快放款/更快审核-权益使用处理
     */
    protected Map<String, Object> getGkProfitUsedInfoExpire(QueryProfitUsedEvent event) {
        log.info("失效会员-查看详情-更快放款/更快审核入参,req={}", JSON.toJSONString(event));
        Map<String, Object> map = new HashMap<>(2);
        map.put("isUse", false);
        ExpireMemberPlusInfoDetailEntity expireMemberPlusInfoDetail = plusInfoDetailRepository.getExpireMemberPlusInfoDetail(event.getUserId(), event.getOrderSn());
        if (expireMemberPlusInfoDetail == null) {
            log.info("失效会员-获取更快放款/更快审核权益使用情况，未获取用户会员信息：{}", event.getUserId());
            return map;
        }
        // ltq 20240705 更快审核、快速放款权益关联借款订单逻辑优化
        if (!PlusConstant.FASTER_CARD_LIST.contains(event.getConfigId())) {
            log.info("失效会员-获取更快放款/更快审核权益使用情况，非固额卡、加速卡、桔享卡、降息卡、重提卡");
            return map;
        }
        //设置会员订单权益使用状态
        wrapPlusProfitUsed(event, map);
        log.info("失效会员-查看详情-更快放款/更快审核,result={}", JSON.toJSONString(map));
        return map;
    }

    private void wrapPlusProfitUsed(QueryProfitUsedEvent event, Map<String, Object> map) {
        List<PlusUseProfitSuPo> profitSuPoList = memberPlusExclusiveRepository.selectByPlusOrderSnCommon(event.getOrderSn());
        // 会员订单都存在时，使用状态=已使用
        if (!CollectionUtils.isEmpty(profitSuPoList)) {
            map.put("isUse", true);
            map.put("orderIdList", profitSuPoList.stream().map(PlusUseProfitSuPo::getOrderSn).collect(Collectors.toList()));
        }
    }

    /**
     * 其他数据处理
     */
    private List<OrdersBase> gkOtherProcess(QueryProfitUsedEvent event, Date createTime) {
        log.info("获取更快放款/更快审核权益使用情况-固额卡、加速卡、桔享卡、降息卡、重提卡：{}", event.getUserId());
        // ltq 20240705 查询会员购卡后7天内订单状态为210、220状态的借款订单
        Date endTime = DateUtil.addDay(createTime, 7);
        return orderExternalRepository.queryOrdersBase(event.getUserId(), Arrays.asList(OrderStateEnum.分期还款中.getState(), OrderStateEnum.已完成.getState()), createTime, endTime, 3);
    }

    /**
     * 获取会员提额-单个类型汇总金额、会员额度到期时间
     */
    public MemberIncQuotaEntity getMemberIncQuotaSumOrEndTime(CustomerCreditChannelDetailVO account, String grade, MemberIncQuotaEntity memberIncQuotavo) {
        List<TempCreditDetailDTO> tempCreditDetailList = account.getTempCreditDetailList();
        log.info("提额额度={}", tempCreditDetailList);
        if (CollectionUtils.isEmpty(tempCreditDetailList)) {
            return memberIncQuotavo;
        }
        String newGrade = NewGradeEnum.getNewGrade(grade);
        TempCreditDetailDTO tempCreditDetailDTO = tempCreditDetailList.stream().filter(t -> t.getTempType().equals(TempTypeEnum.VIP.getCode())).findFirst().orElse(null);
        if (tempCreditDetailDTO == null) {
            return memberIncQuotavo;
        }
        BigDecimal memberIncQuota = tempCreditDetailDTO.getTempCreditItemDetailList().stream().filter(item -> newGrade.equals(item.getItemCode())).map(TempCreditItemDetailDTO::getCreditSumAll).reduce(BigDecimal.ZERO, BigDecimal::add);
        log.info("获取会员提额额度={}", memberIncQuota);
        //额度有效期
        TempCreditItemDetailDTO itemDetail = tempCreditDetailDTO.getTempCreditItemDetailList().stream().filter(item -> newGrade.equals(item.getItemCode())).max((account1, account2) -> LocalDateTimeUtils.compareDate(new Date(account1.getStartDate()), new Date(account2.getStartDate()))).orElse(null);
        log.info("额度有效期itemDetail={}", JSON.toJSONString(itemDetail));
        if (itemDetail == null) {
            log.info("itemDetail={}", JSON.toJSONString(itemDetail));
            return memberIncQuotavo;
        }
        memberIncQuotavo.setMemberIncQuota(memberIncQuota);
        memberIncQuotavo.setEndTime(LocalDateTimeUtils.convertTimeLongToString(itemDetail.getEndDate()));
        return memberIncQuotavo;
    }

    /**
     * 组装会员详页开卡礼、月享
     */
    protected void transform(List<CouponAdminEntity> fromList, List<MemberCouponInfoEntity> toList) {
        log.info("会员详情页开卡礼或月享数组转换 fromList={}", JSONObject.toJSONString(fromList));
        for (CouponAdminEntity couponViewVo : fromList) {
            MemberCouponInfoEntity memberCouponVo = converter.toMemberCouponInfoEntity(couponViewVo);
            String useStartTime = couponViewVo.getUseStartTime();
            String useEndTime = couponViewVo.getUseEndTime();
            if (StringUtils.isNotBlank(useStartTime)) {
                memberCouponVo.setStartTime(LocalDateTimeUtils.formatDateStrToHh(useStartTime));
                memberCouponVo.setEndTime(LocalDateTimeUtils.formatDateStrToHh(useEndTime));
            }
            memberCouponVo.setCouponUserId(couponViewVo.getId());
            setPrice(couponViewVo, memberCouponVo);
            //根据时间、是否可用设置状态
            if (couponViewVo.getCanUse() != null && couponViewVo.getCanUse() == 0) {
                //已使用
                memberCouponVo.setStatus(2);
            } else {
                //去使用
                memberCouponVo.setStatus(1);
                Date nowDate = new Date();
                String endTime = couponViewVo.getUseEndTime();
                Date endDate = LocalDateTimeUtils.parseStringToDate(endTime, LocalDateTimeUtils.DATE_FORMAT_FULL);
                if (nowDate.after(endDate)) {
                    //已过期
                    memberCouponVo.setStatus(3);
                }
            }
            toList.add(memberCouponVo);
        }
        log.info("会员详情页开卡礼或月享数组转换结束 toList={}", JSONObject.toJSONString(toList));
    }

    /**
     * 根据couponViewVo设置优惠券类型和金额
     */
    private void setPrice(CouponAdminEntity coupon, MemberCouponInfoEntity paidMemberCouponVo) {
        // 0-满减优惠券，1-折扣优惠券，2、免息优惠券，3、还款优惠券、4、息费折扣券
        //优惠券类型
        paidMemberCouponVo.setLable(coupon.getCouponCategory());
        if (StringUtils.isNotBlank(coupon.getPrice())) {
            paidMemberCouponVo.setPrice(coupon.getPrice());
        } else {
            //满减券
            if (coupon.getCouponCategory() == 0) {
                paidMemberCouponVo.setPrice(subZeroAndDot(String.valueOf(coupon.getCouponValue())));
                //折扣优惠券
            } else if (coupon.getCouponCategory() == 2) {
                //几折
                paidMemberCouponVo.setPrice(subZeroAndDot(String.valueOf(coupon.getDiscountCoupon())));
                //还款券
            } else if (coupon.getCouponCategory() == 3) {
                paidMemberCouponVo.setPrice(subZeroAndDot(String.valueOf(coupon.getRepaymentCoupon())));
                //息费折扣
            } else if (coupon.getCouponCategory() == 4) {
                //几折
                paidMemberCouponVo.setPrice(subZeroAndDot(String.valueOf(coupon.getDiscountCoupon())));
                //免息券
            } else {
                //几期免息
                paidMemberCouponVo.setPrice(coupon.getInterestFreeCoupon());
            }
        }
        if (coupon.getMinAmount() != null) {
            paidMemberCouponVo.setMinAmount(subZeroAndDot(coupon.getMinAmount()));
        }
        if (coupon.getMaxAmount() != null) {
            paidMemberCouponVo.setMaxAmount(subZeroAndDot(coupon.getMaxAmount()));
        }
    }

    /**
     * 会员详情页开卡礼/月享优惠券处理
     */
    protected void setCouponInfo(List<Integer> couponIdList, Integer sendStatus, List<MemberCouponInfoEntity> memberCouponVos, List<CouponAdminEntity> giftList, String couponUserIds, String couponNos, List<Integer> repayCouponIds, Integer userId) {
        log.info("会员详情页couponIdList:{},sendStatus={},memberCouponVos={},giftList={},couponUserIds={},couponNos={},repayCouponIds={},userId={}", JSONObject.toJSONString(couponIdList), sendStatus, JSONObject.toJSONString(memberCouponVos), JSONObject.toJSONString(giftList), couponUserIds, couponNos, JSONObject.toJSONString(repayCouponIds), userId);
        //未发放，只展示优惠券本身信息
        if (sendStatus == 0) {
            List<JuziCouponEntity> couponList = externalCouponRepository.getCouponByListId(couponIdList);
            log.info("会员详情页开卡礼或月享未发放获取优惠券信息返回结果:{}", JSONObject.toJSONString(couponList));
            if (CollectionUtils.isEmpty(couponList)) {
                return;
            }
            for (JuziCouponEntity juziCoupon : couponList) {
                MemberCouponInfoEntity memberCouponVo = converter.toMemberCouponInfoEntity(juziCoupon);
                memberCouponVo.setStatus(0);
                if (juziCoupon.getUseStartTime() != null) {
                    memberCouponVo.setStartTime(LocalDateTimeUtils.formatDateToStrHh(juziCoupon.getUseStartTime()));
                    memberCouponVo.setEndTime(LocalDateTimeUtils.formatDateToStrHh(juziCoupon.getUseEndTime()));
                }
                setPrice(juziCoupon, memberCouponVo);
                memberCouponVos.add(memberCouponVo);
            }
            return;
        }
        // 已发放，couponUserIds和couponNos为空，根据couponId和userId获取
        if (StringUtils.isBlank(couponUserIds) && StringUtils.isBlank(couponNos)) {
            List<CouponViewVo> list = externalCouponRepository.getCouponByIdsAndSendStatus(couponIdList, sendStatus, userId);
            List<CouponAdminEntity> adminEntities = converter.toCouponAdminEntityList(list);
            if (!CollectionUtils.isEmpty(adminEntities)) {
                giftList.addAll(adminEntities);
            }
            return;
        }
        //根据couponUserId获取普通券
        if (StringUtils.isNotBlank(couponUserIds)) {
            List<CouponViewVo> list = externalCouponRepository.getCouponUserInfo(couponUserIds, userId);
            List<CouponAdminEntity> adminEntities = converter.toCouponAdminEntityList(list);
            if (!CollectionUtils.isEmpty(adminEntities)) {
                giftList.addAll(adminEntities);
            }
        }
        //根据还款券ID获取还款券
        if (StringUtils.isNotBlank(couponNos)) {
            List<CouponViewVo> userRepayCouponList = externalCouponRepository.getUserRepayCoupons(userId, couponNos);
            if (!CollectionUtils.isEmpty(userRepayCouponList)) {
                List<CouponAdminEntity> adminEntities = converter.toCouponAdminEntityList(userRepayCouponList);
                if (!CollectionUtils.isEmpty(adminEntities)) {
                    giftList.addAll(adminEntities);
                }
            }
        }
        log.info("会员详情页开卡礼或月享已发放，有couponUserId记录获取优惠券信息返回结果:{}", JSONObject.toJSONString(giftList));
    }

    /**
     * 会员权益页拒就赔/多买多送惠券处理
     */
    protected List<MemberCouponInfoEntity> setProfitDetailCouponInfo(List<MemberCouponCommonEntity> commonEntityList, int type) {
        log.info("会员权益页memberPlusCoupon={},type={}", JSONObject.toJSONString(commonEntityList), type);
        List<MemberCouponInfoEntity> result = new ArrayList<>();
        Map<Integer, JuziCouponEntity> couponMap = new HashMap<>(commonEntityList.size());
        //设置优惠券领取信息
        for (MemberCouponCommonEntity couponCommonEntity : commonEntityList) {
            JuziCouponEntity coupon = couponMap.get(couponCommonEntity.getCouponId());
            if (coupon == null) {
                coupon = externalCouponRepository.getByCouponId(couponCommonEntity.getCouponId());
                if (coupon == null) {
                    continue;
                }
                couponMap.put(couponCommonEntity.getCouponId(), coupon);
            }
            JuziCouponUser couponUser = null;
            if (Objects.nonNull(couponCommonEntity.getCouponUserId()) || Objects.nonNull(couponCommonEntity.getCouponNos())) {
                log.info("用户优惠券ID={},couponNos:{}", couponCommonEntity.getCouponUserId(), couponCommonEntity.getCouponNos());
                //还款券
                if (coupon.getCouponCategory().equals(CouponCategoryEnum.COUPON_CATEGORY_3.getCode())) {
                    couponUser = getUserRepayCoupon(couponCommonEntity);
                } else {
                    couponUser = externalCouponRepository.getCouponUserById(couponCommonEntity.getCouponUserId());
                }
            }
            log.info("couponUser={}", JSONObject.toJSONString(couponUser));
            MemberCouponInfoEntity entity = createCouponEntity(couponCommonEntity, couponUser, coupon, type);
            result.add(entity);
        }
        return result;
    }

    /**
     * 获取领取的还款优惠券
     */
    protected JuziCouponUser getUserRepayCoupon(MemberCouponCommonEntity couponCommonEntity) {
        JuziCouponUser couponUser = null;
        List<CouponViewVo> userRepayCouponList = externalCouponRepository.getUserRepayCoupons(couponCommonEntity.getMemberId(), couponCommonEntity.getCouponNos());
        if (!CollectionUtils.isEmpty(userRepayCouponList)) {
            CouponViewVo couponViewVo = userRepayCouponList.get(0);
            couponUser = converter.toJuziCouponUser(couponViewVo);
            couponUser.setUseStartTime(LocalDateTimeUtils.parseStringToDate(couponViewVo.getUseStartTime(), LocalDateTimeUtils.DATE_FORMAT_FULL));
            couponUser.setUseEndTime(LocalDateTimeUtils.parseStringToDate(couponViewVo.getUseEndTime(), LocalDateTimeUtils.DATE_FORMAT_FULL));
        }
        log.info("获取领取的还款优惠券={}", JSONObject.toJSONString(couponUser));
        return couponUser;
    }

    /**
     * 获取某个权益的发放计划，包含过期
     */
    protected List<MemberPlusSendPlanEntity> getMemberPlusSendPlanEntity(String orderSn, Integer modelId) {
        List<MemberPlusSendPlanEntity> plan = sendPlanModel.getSendPlanByOrderSn(orderSn);
        if (CollectionUtils.isEmpty(plan)) {
            return null;
        }
        return plan.stream().filter(e -> Objects.equals(e.getModelId(), modelId)).collect(Collectors.toList());
    }

    /**
     * 获取会员订单下某个权益的发放计划，不包含过期
     */
    protected List<MemberPlusSendPlanEntity> getSendPlanList(String plusOrderSn, Integer modelId) {
        List<MemberPlusSendPlanEntity> list = memberPlusSendPlanRepository.listByOrderSn(plusOrderSn);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.stream().filter(e -> Objects.equals(e.getModelId(), modelId)).collect(Collectors.toList());
    }

    /**
     * 获取发放计划-按多条件查询
     */
    protected List<MemberPlusSendPlanEntity> getSendPlanList(MemberPlusProfitSendPlanQueryReq req) {
        return memberPlusSendPlanRepository.querySendPlanList(req);
    }

    /**
     * 赋值优惠券领取标识信息
     */
    protected void setCommonCouponReceiveInfo(List<MemberCouponCommonEntity> commonEntityList) {
        for (MemberCouponCommonEntity entity : commonEntityList) {
            MemberPlusCouponRecordEntity record = plusCouponRepository.getBySendPlanId(entity.getId());
            if (record != null) {
                entity.setCouponNos(record.getCouponNos());
                entity.setCouponUserId(record.getCouponUserId());
            }
        }
    }

    /**
     * 获取执行计划状态
     *
     * <AUTHOR>
     * @version 1.0
     * @date 2024/2/28 17:39
     */
    protected Integer getSendPlanStatus(MemberPlusSendPlanEntity plan) {
        // 默认值
        Integer result = CommonConstant.TWO;
        // 1-未生效 2-达成中 3-已就绪 4-已发放 5-已过期 6-已取消
        PlusSendPlanStatusEnum statusEnum = PlusSendPlanStatusEnum.getType(plan.getSendStatus());
        if (statusEnum != null) {
            switch (statusEnum) {
                case NOT_EFFECTIVE:
                case CONDITIONAL:
                case READY:
                    // 未领取
                    result = CommonConstant.ZERO;
                    break;
                case DONE:
                    // 已领取
                    result = CommonConstant.ONE;
                    break;
                default:
                    // 已失效
                    result = CommonConstant.TWO;
                    break;
            }
        }
        return result;
    }

    /**
     * 设置使用时间
     */
    protected void setUserTime(JuziCouponEntity coupon, MemberCouponInfoEntity memberPlusVo) {
        if (coupon.getUseStartTime() != null) {
            String startTime = LocalDateTimeUtils.parseDateToString(coupon.getUseStartTime(), LocalDateTimeUtils.DATE_FORMAT_COMPACT_DIAN);
            String endTime = LocalDateTimeUtils.parseDateToString(coupon.getUseEndTime(), LocalDateTimeUtils.DATE_FORMAT_COMPACT_DIAN);
            memberPlusVo.setUserTime(startTime + "-" + endTime);
        } else {
            memberPlusVo.setUserTime(coupon.getValidDays() + "天");
        }
    }

    /**
     * 设置优惠券信息
     */
    protected void buildCouponIndex(List<MemberCouponInfoEntity> result, List<PlusCouponIndexEntity> couponIndexYx) {
        List<Integer> couponIdList = couponIndexYx.stream().map(PlusCouponIndexEntity::getCouponId).collect(Collectors.toList());
        List<JuziCouponEntity> couponList = externalCouponRepository.getCouponByListId(couponIdList);
        for (JuziCouponEntity coupon : couponList) {
            MemberCouponInfoEntity entity = converter.toMemberCouponInfoEntity(coupon);
            setPrice(coupon, entity);
            //设置使用时间
            setUserTime(coupon, entity);
            result.add(entity);
        }
    }

    /**
     * 会员权益页拒就赔/多买多送惠券处理
     */
    private MemberCouponInfoEntity createCouponEntity(MemberCouponCommonEntity couponCommonEntity, JuziCouponUser couponUser, JuziCouponEntity coupon, int type) {
        log.info("会员权益页memberPlusCoupon={},couponUser={},coupon={},type{}", JSONObject.toJSONString(couponCommonEntity), JSONObject.toJSONString(couponUser), JSONObject.toJSONString(coupon), type);
        MemberCouponInfoEntity couponMemberPlusVo = new MemberCouponInfoEntity();
        couponMemberPlusVo.setCouponId(coupon.getCouponId());
        couponMemberPlusVo.setTitle(coupon.getCouponName());
        couponMemberPlusVo.setUseCode(coupon.getUseCode());
        couponMemberPlusVo.setSceneCode(coupon.getSceneCode());
        couponMemberPlusVo.setCouponCategory(coupon.getCouponCategory());
        couponMemberPlusVo.setSellerId(coupon.getSellerId());
        couponMemberPlusVo.setUrl("borrow_money/index.html");
        couponMemberPlusVo.setPeriod(coupon.getPeriod());
        couponMemberPlusVo.setReceiveStartTime(coupon.getSendStartTime());
        couponMemberPlusVo.setReceiveEndTime(coupon.getSendEndTime());
        setPrice(coupon, couponMemberPlusVo);
        setUserTime(coupon, couponMemberPlusVo);
        if (Objects.nonNull(couponUser)) {
            coupon.setUseEndTime(couponUser.getUseEndTime());
            coupon.setUseStartTime(couponUser.getUseStartTime());
            //type = 1 拒就赔,type = 2 多买多送，时间根式返回"yyyy.MM.DD"
            couponMemberPlusVo.setStartTime(LocalDateTimeUtils.formatDateToStrHh(coupon.getUseStartTime()));
            couponMemberPlusVo.setEndTime(LocalDateTimeUtils.formatDateToStrHh(coupon.getUseEndTime()));
            //设置单个优惠券的状态
            if (couponUser.getCanUse() == 0) {
                //已使用
                couponMemberPlusVo.setStatus(2);
            } else {
                log.info("canUse={}", couponUser.getCanUse());
                //去使用
                couponMemberPlusVo.setStatus(1);
                Date nowDate = new Date();
                if (nowDate.after(couponUser.getUseEndTime())) {
                    //已过期
                    couponMemberPlusVo.setStatus(3);
                }
            }
        } else {
            //未发放
            couponMemberPlusVo.setStatus(0);
            log.info("未发放={}", couponMemberPlusVo.getStatus());
        }
        //优惠券类型
        couponMemberPlusVo.setLable(coupon.getCouponCategory());
        //优惠券描述
        couponMemberPlusVo.setRemark(coupon.getRemark());
        //优惠券类型1.品类券 2.品牌券 3.活动券 4.单品券 5.全品券
        couponMemberPlusVo.setCouponType(coupon.getCouponType());
        couponMemberPlusVo.setType(type);
        couponMemberPlusVo.setReceiveStatus(couponCommonEntity.getReceiveStatus());
        couponMemberPlusVo.setReachStatus(couponCommonEntity.getReachStatus());
        if (type == 2) {
            String desc = "";
            if (couponCommonEntity.getOrderMoney() != null && couponCommonEntity.getOrderMoney().compareTo(BigDecimal.ZERO) > 0) {
                desc = "满" + subZeroAndDot(couponCommonEntity.getOrderMoney().toString()) + "元送";
            }
            if (couponCommonEntity.getOrdersNumber() != null) {
                couponMemberPlusVo.setOrderNum(String.valueOf(couponCommonEntity.getOrdersNumber()));
            }
            couponMemberPlusVo.setPlusCouponDesc(desc);
        } else {
            PlusProgramRejectionPo rejectionPo = programCouponRepository.getRejectById(couponCommonEntity.getRejectionId());
            if (rejectionPo != null) {
                if (1 == rejectionPo.getType()) {
                    couponMemberPlusVo.setPlusCouponDesc("下单被拒");
                } else {
                    couponMemberPlusVo.setPlusCouponDesc("认证被拒");
                }
            }
        }
        couponMemberPlusVo.setMemberPlusCouponId(couponCommonEntity.getId());
        if (!CollectionUtils.isEmpty(coupon.getProductList())) {
            Product product = coupon.getProductList().get(0);
            if (product != null) {
                couponMemberPlusVo.setProductId(product.getId());
            }
        }
        log.info("权益页面data={}", JSONObject.toJSONString(couponMemberPlusVo));
        return couponMemberPlusVo;
    }

    /**
     * 有效状态
     */
    protected List<Integer> getValidSendPlanStatus() {
        return Arrays.asList(PlusSendPlanStatusEnum.NOT_EFFECTIVE.getValue(), PlusSendPlanStatusEnum.CONDITIONAL.getValue(), PlusSendPlanStatusEnum.READY.getValue(), PlusSendPlanStatusEnum.DONE.getValue());
    }

    /**
     * 构建couponUserIds
     */
    protected String buildStringCouponUserIds(List<MemberCouponCommonEntity> list) {
        return org.apache.commons.lang3.StringUtils.join(list.stream().map(MemberCouponCommonEntity::getCouponUserId).filter(Objects::nonNull).collect(Collectors.toList()), ",");
    }

    /**
     * 构建couponNos
     */
    protected String buildStringCouponNos(List<MemberCouponCommonEntity> list) {
        return org.apache.commons.lang3.StringUtils.join(list.stream().map(MemberCouponCommonEntity::getCouponNos).filter(StringUtils::isNotBlank).collect(Collectors.toList()), ",");
    }
}
