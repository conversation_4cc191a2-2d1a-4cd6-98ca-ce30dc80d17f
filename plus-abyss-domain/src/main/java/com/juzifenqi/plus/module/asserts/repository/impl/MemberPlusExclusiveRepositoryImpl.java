package com.juzifenqi.plus.module.asserts.repository.impl;

import com.juzifenqi.plus.module.asserts.model.contract.IMemberPlusExclusiveRepository;
import com.juzifenqi.plus.module.asserts.repository.dao.IPlusUseProfitSuMapper;
import com.juzifenqi.plus.module.asserts.repository.po.PlusUseProfitSuPo;

import java.util.Collections;
import java.util.List;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

/**
 * 用户专属权益
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/24 16:48
 */
@Slf4j
@Repository
public class MemberPlusExclusiveRepositoryImpl implements IMemberPlusExclusiveRepository {

    @Autowired
    private IPlusUseProfitSuMapper plusUseProfitSuMapper;

    @Override
    public void savePlusUseProfitSuPo(PlusUseProfitSuPo po) {
        PlusUseProfitSuPo plusUseProfitSuPo = plusUseProfitSuMapper.loadPlusOrder(
                po.getPlusOrderSn(), po.getOrderSn());
        if (plusUseProfitSuPo != null) {
            log.info("加速权益处理-已经有使用加速权益记录-userId={},会员订单号={},借款单订单号={}",
                    po.getUserId(), po.getPlusOrderSn(), po.getOrderSn());
            return;
        }
        plusUseProfitSuMapper.savePlusUseProfitSu(po);
    }

    /**
     * 根据用户id查询
     */
    @Override
    public List<PlusUseProfitSuPo> selectByUserId(Integer userId) {
        return plusUseProfitSuMapper.selectByUserId(userId);
    }

    /**
     * 根据会员订单号查询记录
     *
     * @param plusOrderSn 会员订单号
     * @return 专属权益记录，不存在则返回null
     */
    @Override
    public PlusUseProfitSuPo selectByPlusOrderSn(String plusOrderSn) {
        return plusUseProfitSuMapper.selectByPlusOrderSn(plusOrderSn);
    }

    @Override
    public List<PlusUseProfitSuPo> selectByPlusOrderSnCommon(String plusOrderSn) {
        if (StringUtils.isBlank(plusOrderSn)) {
            return Collections.emptyList();
        }
        return plusUseProfitSuMapper.selectByPlusOrderSnCommon(plusOrderSn);
    }

}
