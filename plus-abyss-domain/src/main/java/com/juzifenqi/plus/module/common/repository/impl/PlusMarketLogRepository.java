package com.juzifenqi.plus.module.common.repository.impl;

import com.alibaba.fastjson.JSON;
import com.juzifenqi.alita.utils.DateUtil;
import com.juzifenqi.magic.bean.enums.SceneCodeEnum;
import com.juzifenqi.plus.dto.req.market.*;
import com.juzifenqi.plus.module.common.IPlusMarketingRepository;
import com.juzifenqi.plus.module.common.repository.dao.IPlusMarketLogBackupMapper;
import com.juzifenqi.plus.module.common.repository.dao.IPlusMarketLogMapper;
import com.juzifenqi.plus.module.common.repository.po.PlusMarketLogPo;
import com.juzifenqi.plus.module.market.model.contract.entity.PlusLoanConfirmMarketEntity;
import com.juzifenqi.plus.module.market.model.contract.entity.PlusLoanMarketEntity;
import com.juzifenqi.plus.module.market.model.contract.entity.PlusOrderListMarketEntity;
import com.juzifenqi.plus.module.market.model.contract.entity.PlusPaySuccessMarketEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 营销日志
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/25 17:34
 */
@Service
@Slf4j
public class PlusMarketLogRepository implements IPlusMarketingRepository {

    @Resource
    private IPlusMarketLogMapper plusMarketLogMapper;
    @Resource
    private IPlusMarketLogBackupMapper plusMarketLogBackupMapper;


    @Override
    public void insertPaySuccessLog(PlusPaySuccessMarketReq req ,PlusPaySuccessMarketEntity entity) {
        PlusMarketLogPo po = new PlusMarketLogPo();
        po.setUserId(req.getUserId());
        po.setChannelId(req.getChannelId());
        po.setBizSource(req.getBizSource() == null ? 1 : req.getBizSource());
        po.setSceneCode(SceneCodeEnum.PAY_SUCCESS.getCode());
        po.setConfigId(entity.getConfigId());
        po.setProgramId(entity.getProgramId());
        po.setProgramPrice(entity.getMallMobilePrice());
        po.setShuntSupplierId(entity.getShuntSupplierId());
        po.setAfterPayState(entity.getAfterPayState());
        po.setNoMarketingReason(entity.getNoMarketReason());
        po.setCreateTime(LocalDateTime.now());

        insertMarketLog(po);
    }

    @Override
    public void insertOrderListMarketLog(PlusOrderListMarketReq req, PlusOrderListMarketEntity entity) {
        PlusMarketLogPo po = new PlusMarketLogPo();
        po.setUserId(req.getUserId());
        po.setChannelId(req.getChannelId());
        po.setBizSource(req.getBizSource() == null ? 1 : req.getBizSource());
        po.setSceneCode(SceneCodeEnum.ORDER_LIST.getCode());
        po.setConfigId(entity.getConfigId());
        po.setProgramId(entity.getProgramId());
        po.setProgramPrice(entity.getMallMobilePrice());
        po.setDiscountRate(entity.getDiscountRate());
        po.setDiscountPrice(entity.getDiscountPrice());
        po.setRaiseAmount(entity.getRaiseAmount());
        po.setShuntSupplierId(entity.getShuntSupplierId());
        po.setAfterPayState(entity.getAfterPayState());
        po.setNoMarketingReason(entity.getNoMarketReason());
        po.setCreateTime(LocalDateTime.now());

        insertMarketLog(po);
    }

    @Override
    public void insertLoanConfirmMarketLog(PlusLoanConfirmMarketReq req, PlusLoanConfirmMarketEntity entity) {
        PlusMarketLogPo po = new PlusMarketLogPo();
        po.setUserId(req.getUserId());
        po.setChannelId(req.getChannelId());
        po.setBizSource(req.getBizSource() == null ? 1 : req.getBizSource());
        po.setSceneCode(SceneCodeEnum.CONFIRM_LOAN.getCode());
        po.setConfigId(entity.getConfigId());
        po.setProgramId(entity.getProgramId());
        po.setProgramPrice(entity.getMallMobilePrice());
        po.setDiscountRate(entity.getDiscountRate());
        po.setDiscountPrice(entity.getDiscountPrice());
        po.setShuntSupplierId(entity.getShuntSupplierId());
        po.setAfterPayState(entity.getAfterPayState());
        po.setRaiseAmount(entity.getRaiseAmount());
        po.setNoMarketingReason(entity.getNoMarketReason());
        po.setCreateTime(LocalDateTime.now());

        insertMarketLog(po);

    }

    @Override
    public void insertLoanMarketLog(PlusLoanMarketReq req, PlusLoanMarketEntity entity) {
        PlusMarketLogPo po = new PlusMarketLogPo();
        po.setUserId(req.getUserId());
        po.setChannelId(req.getChannelId());
        po.setBizSource(req.getBizSource() == null ? 1 : req.getBizSource());
        po.setSceneCode(SceneCodeEnum.LOAN.getCode());
        po.setConfigId(entity.getConfigId());
        po.setProgramId(entity.getProgramId());
        po.setProgramPrice(entity.getMallMobilePrice());
        po.setDiscountRate(entity.getDiscountRate());
        po.setRaiseAmount(entity.getRaiseAmount());
        po.setDiscountPrice(entity.getDiscountPrice());
        po.setShuntSupplierId(entity.getShuntSupplierId());
        po.setAfterPayState(entity.getAfterPayState());
        po.setNoMarketingReason(entity.getNoMarketReason());
        po.setCreateTime(LocalDateTime.now());

        insertMarketLog(po);
    }

    @Override
    public void insertOrderDetailMarketLog(PlusBillListMarketReq req, PlusOrderListMarketEntity entity) {
        PlusMarketLogPo po = new PlusMarketLogPo();
        po.setUserId(req.getUserId());
        po.setChannelId(req.getChannelId());
        po.setBizSource(req.getBizSource() == null ? 1 : req.getBizSource());
        po.setSceneCode(SceneCodeEnum.ORDER_DETAIL.getCode());
        po.setConfigId(entity.getConfigId());
        po.setProgramId(entity.getProgramId());
        po.setProgramPrice(entity.getMallMobilePrice());
        po.setDiscountRate(entity.getDiscountRate());
        po.setDiscountPrice(entity.getDiscountPrice());
        po.setRaiseAmount(entity.getRaiseAmount());
        po.setShuntSupplierId(entity.getShuntSupplierId());
        po.setAfterPayState(entity.getAfterPayState());
        po.setNoMarketingReason(entity.getNoMarketReason());
        po.setCreateTime(LocalDateTime.now());

        insertMarketLog(po);
    }


    private void insertMarketLog(PlusMarketLogPo logPo) {
        if (logPo.getChannelId() == 803002) {
            return;
        }
        try {
            plusMarketLogMapper.insert(logPo);
        } catch (Exception e) {
            log.error("插入会员营销日志失败,log:{}, error:", JSON.toJSONString(logPo),e);
        }
    }

    @Override
    public PlusMarketLogPo getMarketLog(Integer userId, Date orderCreateTime) {
        return plusMarketLogMapper.getMarketLog(userId, orderCreateTime);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void migrateMarketLog(Integer beforeDays, Integer limit) {
        Date time = DateUtil.getDateBefore(new Date(), beforeDays);
        List<PlusMarketLogPo> plusMarketLogPoList = plusMarketLogMapper.getMarketByTime(time, limit);
        if (!plusMarketLogPoList.isEmpty()) {
            //先插入后删除 不插入
            //plusMarketLogBackupMapper.batchInsert(plusMarketLogPoList);
            plusMarketLogMapper.deleteByIds(plusMarketLogPoList.stream().map(PlusMarketLogPo::getId).collect(Collectors.toList()));
        }
    }
}
