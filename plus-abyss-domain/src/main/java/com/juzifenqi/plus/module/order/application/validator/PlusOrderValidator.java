package com.juzifenqi.plus.module.order.application.validator;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Preconditions;
import com.juzifenqi.enumeration.OrderStateEnum;
import com.juzifenqi.member.entity.member.MemberInfo;
import com.juzifenqi.order.dao.entity.Orders;
import com.juzifenqi.order.dto.OrderSimpleInfoDTO;
import com.juzifenqi.plus.constants.RedisConstantPrefix;
import com.juzifenqi.plus.dto.pojo.CreateOrderContext;
import com.juzifenqi.plus.dto.req.admin.PlusOrderDetailQueryReq;
import com.juzifenqi.plus.dto.req.admin.PlusOrderInfoQueryReq;
import com.juzifenqi.plus.enums.BusinessTypeEnum;
import com.juzifenqi.plus.enums.ChannelEnum;
import com.juzifenqi.plus.enums.CreateOrderSceneEnum;
import com.juzifenqi.plus.enums.JuziPlusEnum;
import com.juzifenqi.plus.enums.OrderCallbackEnum;
import com.juzifenqi.plus.enums.OrderRenewEnum;
import com.juzifenqi.plus.enums.PayRefundTypeEnum;
import com.juzifenqi.plus.enums.PlusCancelTypeEnum;
import com.juzifenqi.plus.enums.PlusModelEnum;
import com.juzifenqi.plus.enums.PlusOrderPayTypeEnum;
import com.juzifenqi.plus.enums.PlusOrderRelationBusinessType;
import com.juzifenqi.plus.enums.PlusOrderStateEnum;
import com.juzifenqi.plus.enums.RefundStateEnum;
import com.juzifenqi.plus.enums.VipErrorEnum;
import com.juzifenqi.plus.enums.refund.RefundInfoStateEnum;
import com.juzifenqi.plus.exception.PlusAbyssException;
import com.juzifenqi.plus.module.asserts.model.MemberPlusQueryModel;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusInfoDetailEntity;
import com.juzifenqi.plus.module.common.ICreditExternalRepository;
import com.juzifenqi.plus.module.common.IMemberExternalRepository;
import com.juzifenqi.plus.module.order.model.IPlusOrderDefrayModel;
import com.juzifenqi.plus.module.order.model.IPlusOrderRefundInfoModel;
import com.juzifenqi.plus.module.order.model.PlusOrderQueryModel;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.RiskFuseEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.callback.OrderRefundNotifyEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.refund.PlusOrderRefundInfoEntity;
import com.juzifenqi.plus.module.order.model.contract.external.IOrderExternalRepository;
import com.juzifenqi.plus.module.order.model.contract.external.IRiskExternalRepository;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderCancelEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderCreateEvent;
import com.juzifenqi.plus.module.order.model.event.order.UpdOrderStateEvent;
import com.juzifenqi.plus.module.order.model.impl.strategy.HandlerContext;
import com.juzifenqi.plus.module.program.model.IPlusProgramQueryModel;
import com.juzifenqi.plus.module.program.model.contract.entity.PlusLiftAmountEntity;
import com.juzifenqi.plus.module.program.model.contract.entity.PlusProgramLmkVirtualEntity;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramEntity;
import com.juzifenqi.plus.utils.ParamCheckUtils;
import com.juzifenqi.plus.utils.PlusConcatUtils;
import com.juzifenqi.plus.utils.RedisLock;
import com.juzishuke.credit.vo.CustomerCreditChannelDetailVO;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 下单校验器
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class PlusOrderValidator {

    @Autowired
    private IPlusProgramQueryModel    programQueryModel;
    @Autowired
    private ICreditExternalRepository creditExternalRepository;
    @Autowired
    private IRiskExternalRepository   riskExternalRepository;
    @Autowired
    private RedisLock                 redisLock;
    @Autowired
    private IOrderExternalRepository  orderExternalRepository;
    @Autowired
    private PlusOrderQueryModel       plusOrderQueryModel;
    @Autowired
    private MemberPlusQueryModel      plusQueryModel;
    @Autowired
    private HandlerContext            handlerContext;
    @Autowired
    private IMemberExternalRepository memberExternalRepository;
    @Autowired
    private IPlusOrderDefrayModel     orderDefrayModel;
    @Autowired
    private IPlusOrderRefundInfoModel orderRefundInfoModel;

    /**
     * 下单校验
     */
    public void createOrderValidator(PlusOrderCreateEvent event, PlusProgramEntity program) {
        log.info("下单校验开始，event={}，program={}", JSON.toJSONString(event),
                JSON.toJSONString(program));
        String redisKey = PlusConcatUtils.symbolBelowStr(RedisConstantPrefix.MEMBER_OPEN_PLUS_KEY,
                event.getChannelId(), event.getProgramId(), event.getUserId());
        boolean lock = redisLock.lock(redisKey, "1", 5);
        if (!lock) {
            log.info("用户:{} 5秒内重复请求开通会员 key:{}", event.getUserId(), redisKey);
            throw new PlusAbyssException("短时间内不允许重复购买！");
        }
        // 后付款前置校验
        if (Objects.equals(event.getPayType(), PlusOrderPayTypeEnum.PAY_AFTER.getValue()) || Objects.equals(event.getPayType(), PlusOrderPayTypeEnum.PAY_FIRST_PERIOD.getValue())) {
            handlerContext.preProcessorAfterPlus(event, program);
        }
        // 创单校验
        checkProgram(event, program);
        checkLmk(event);
        checkRiskQuota(event, program);
        checkParam(event, program);
    }

    /**
     * 校验参数
     */
    private void checkParam(PlusOrderCreateEvent plusOrderCreateEvent, PlusProgramEntity program) {
        if (plusOrderCreateEvent.getCreateOrderContext() != null
                && plusOrderCreateEvent.getCreateOrderContext().getRelationBusinessType() != null) {
            PlusOrderRelationBusinessType type = PlusOrderRelationBusinessType.getByCode(
                    plusOrderCreateEvent.getCreateOrderContext().getRelationBusinessType());
            if (type == null) {
                throw new PlusAbyssException("无效的订单关联类型");
            }
        }
        // 20231016 zjf 小额月卡首单创建，借款单非501或611状态不允许创单
        if (JuziPlusEnum.XEYK_CARD.getCode() == program.getConfigId()
                && plusOrderCreateEvent.getCreateOrderContext() != null && StringUtils.isNotBlank(
                plusOrderCreateEvent.getCreateOrderContext().getLoanOrderSn())) {
            OrderSimpleInfoDTO order = orderExternalRepository.getByOrderSn(
                    plusOrderCreateEvent.getCreateOrderContext().getLoanOrderSn());
            if (order == null) {
                throw new PlusAbyssException("借款单不存在");
            }
            if (!Objects.equals(OrderStateEnum.交易复核中.getState(), order.getOrderState())
                    && !Objects.equals(OrderStateEnum.备货中.getState(), order.getOrderState())) {
                throw new PlusAbyssException("您的订单状态已更新，请刷新");
            }
        }
        // 20231120 zjf 回调地址不能为空
        if (plusOrderCreateEvent.getCreateOrderContext() != null) {
            if (MapUtils.isNotEmpty(
                    plusOrderCreateEvent.getCreateOrderContext().getCallbackUrlList())) {
                Map<String, String> callbackUrlList = plusOrderCreateEvent.getCreateOrderContext()
                        .getCallbackUrlList();
                for (Entry<String, String> entry : callbackUrlList.entrySet()) {
                    if (StringUtils.isBlank(entry.getKey()) || StringUtils.isBlank(
                            entry.getValue())) {
                        throw new PlusAbyssException("回调地址不允许为空");
                    }
                    if (OrderCallbackEnum.getByCode(entry.getKey()) == null) {
                        throw new PlusAbyssException("回调地址key无效：" + entry.getKey());
                    }
                }
            }
            String paySuccessReturnUrl = plusOrderCreateEvent.getCreateOrderContext()
                    .getPaySuccessReturnUrl();
            if (StringUtils.isNotBlank(paySuccessReturnUrl) && paySuccessReturnUrl.length() > 500) {
                throw new PlusAbyssException("支付完成跳转url长度不允许超过500");
            }
        }
        // 20240115 zjf 商城渠道加速卡借款单状态判断
        if (JuziPlusEnum.EXPEDITE_CARD.getCode() == program.getConfigId() && !Objects.equals(
                plusOrderCreateEvent.getSceneCode(), CreateOrderSceneEnum.LD.getCode())) {
            Integer userId = plusOrderCreateEvent.getUserId();
            log.info("商城渠道加速卡非落地页校验借款单状态开始：{}", userId);
            Orders orders = orderExternalRepository.lastOneProcessOrder(userId);
            if (orders == null) {
                throw new PlusAbyssException("您的订单状态已更新，请刷新");
            }
        }
    }

    /**
     * 校验方案
     */
    private void checkProgram(PlusOrderCreateEvent plusOrderCreateEvent,
            PlusProgramEntity plusProgramEntity) {
        if (Objects.equals(plusOrderCreateEvent.getRenew(), OrderRenewEnum.RENEW.getCode())
                && JuziPlusEnum.XEYK_CARD.getCode() == plusProgramEntity.getConfigId()) {
            log.info("小额月卡续费单，不校验方案信息, userId = {}, programId = {}",
                    plusOrderCreateEvent.getUserId(), plusOrderCreateEvent.getProgramId());
            return;
        }
        if (!Objects.equals(plusOrderCreateEvent.getChannelId(), plusProgramEntity.getChannel())) {
            log.error("会员方案渠道与购买渠道不一至");
            throw new PlusAbyssException("该方案已经下架或失效");
        }
        if (plusProgramEntity.getConfigId() != JuziPlusEnum.RDZX_CARD.getCode() && (
                plusProgramEntity.getStatus() != 1
                        || plusProgramEntity.getProgrammeStatus() != 1)) {
            throw new PlusAbyssException("该方案已经下架或失效，请重新选择后再购买！");
        }
        int total = programQueryModel.countProModelByProgramId(plusProgramEntity.getProgramId(),
                PlusModelEnum.ZXTE.getModelId());
        if (total == 0) {
            return;
        }
        CustomerCreditChannelDetailVO accountInfo = creditExternalRepository.getAccountInfo(
                plusOrderCreateEvent.getUserId(), true);
        if (accountInfo == null) {
            return;
        }
        BigDecimal allFixedAmount = accountInfo.getPermanentCreditSumAll();
        BigDecimal borrowCredit = accountInfo.getBorrowCreditSumAll();
        boolean result = !(allFixedAmount != null && allFixedAmount.compareTo(BigDecimal.ZERO) > 0
                && borrowCredit != null && borrowCredit.compareTo(BigDecimal.ZERO) == 0);
        if (!result) {
            throw new PlusAbyssException("抱歉，您不能购买含有专享提额权益的方案");
        }
    }

    /**
     * 校验联名卡
     */
    private void checkLmk(PlusOrderCreateEvent plusOrderCreateEvent) {
        if (!BusinessTypeEnum.LMK.getCode().equals(plusOrderCreateEvent.getBusinessType())) {
            log.info("非联名卡订单：{}", plusOrderCreateEvent.getUserId());
            return;
        }
        if (plusOrderCreateEvent.getCreateOrderContext() == null
                || plusOrderCreateEvent.getCreateOrderContext().getVirtualId() == null) {
            throw new PlusAbyssException("请选择一个联名卡权益开通");
        }
        //  方案下是否配置联名卡权益
        int total = programQueryModel.countProModelByProgramId(plusOrderCreateEvent.getProgramId(),
                PlusModelEnum.LMQY.getModelId());
        if (total == 0) {
            throw new PlusAbyssException("当前方案未配置联名卡权益");
        }
        // 校验方案下是否配置传入的虚拟权益
        PlusProgramLmkVirtualEntity virtual = programQueryModel.getVirtualById(
                plusOrderCreateEvent.getCreateOrderContext().getVirtualId());
        if (virtual == null) {
            throw new PlusAbyssException("联名卡权益无效，请重新选择开通");
        }
        // 下架
        if (virtual.getVirtualStatus() == 0) {
            throw new PlusAbyssException("当前联名卡权益已下架，请重新选择开通");
        }
        List<PlusProgramLmkVirtualEntity> lmkList = programQueryModel.getLmkList(
                plusOrderCreateEvent.getProgramId());
        if (CollectionUtils.isEmpty(lmkList)) {
            throw new PlusAbyssException("当前方案未配置联名卡对应权益数据");
        }
        // 判断是否同一个权益
        if (lmkList.stream().noneMatch(e -> virtual.getSku().equals(e.getSku()))) {
            throw new PlusAbyssException("当前方案未配置该联名卡权益，请重新选择开通");
        }
        if (plusOrderCreateEvent.getCreateOrderContext() == null) {
            plusOrderCreateEvent.setCreateOrderContext(new CreateOrderContext());
        }
    }

    /**
     * 校验风控熔断
     */
    private void checkRiskQuota(PlusOrderCreateEvent plusOrderCreateEvent,
            PlusProgramEntity plusProgramEntity) {
        // 20230711 zjf 加速卡、小额月卡、还款卡不走熔断
        Integer configId = plusProgramEntity.getConfigId();
        if (configId.equals(JuziPlusEnum.EXPEDITE_CARD.getCode()) || configId.equals(
                JuziPlusEnum.XEYK_CARD.getCode()) || configId.equals(
                JuziPlusEnum.REPAY_CARD.getCode())) {
            log.info("下单调风控接口判断是否熔断，加速卡/小额月卡/还款卡不校验");
            return;
        }
        Integer programId = plusOrderCreateEvent.getProgramId();
        // 获取权益信息
        int total = programQueryModel.countProModelByProgramId(programId,
                PlusModelEnum.HYTE.getModelId());
        //方案无会员提额权益，可直接购买
        if (total == 0) {
            return;
        }
        PlusLiftAmountEntity liftAmount = programQueryModel.getByProgramId(programId);
        if (liftAmount != null) {
            Integer memberId = plusOrderCreateEvent.getUserId();
            Integer channel = plusOrderCreateEvent.getChannelId();
            String grade = liftAmount.getGrade();
            RiskFuseEntity riskFuse = riskExternalRepository.checkFused(memberId, channel, grade);
            if (riskFuse.getCode() != null && riskFuse.getCode() == 10100001) {
                throw new PlusAbyssException(VipErrorEnum.PLUS_ERROR_500002);
            }
            if (riskFuse.getFuse() == null || riskFuse.getFuse()) {
                throw new PlusAbyssException(VipErrorEnum.PLUS_ERROR_500002);
            }
        }
    }

    /**
     * 重复开卡校验
     */
    public boolean repeatOrderValidator(PlusOrderEntity plusOrderEntity) {
        Integer configId = plusOrderEntity.getConfigId();
        // 融担咨询卡不需要校验重复支付
        if (JuziPlusEnum.RDZX_CARD.getCode() == configId) {
            return true;
        }
        Integer userId = plusOrderEntity.getUserId();
        String key = PlusConcatUtils.symbolBelowStr(RedisConstantPrefix.MEMBER_PLUS_INFO_KEY,
                configId, userId);
        boolean lock = redisLock.lock(key, "loadMemberPlusInfo", 3600);
        log.info("重复开通会员校验结果：{}，{}", key, lock);
        return lock;
    }

    /**
     * 取消订单校验
     */
    public void cancelOrderValidator(PlusOrderCancelEvent event) {
        log.info("取消订单校验开始={}", JSON.toJSONString(event));
        ParamCheckUtils.checkNull(event.getPlusOrderSn(), "会员单号不能为空");
        ParamCheckUtils.checkNull(event.getOptUserName(), "操作人姓名不能为空");
        ParamCheckUtils.checkNull(event.getOptUserId(), "操作人id不能为空");
        ParamCheckUtils.checkNull(event.getCancelType(), "取消方式不能为空");
        // 按比例取消，需要传入退款比例且校验比例是否正确
        if (event.getCancelType() == PlusCancelTypeEnum.RATIO.getValue() && (
                event.getRefundRate() == null
                        || event.getRefundRate().compareTo(BigDecimal.ZERO) < 0
                        || event.getRefundRate().compareTo(BigDecimal.ONE) > 1)) {
            throw new PlusAbyssException("退款比例不能为空且必须在0%-100%范围内");
        }
    }

    /**
     * 客服变更状态校验
     */
    public PlusOrderEntity updOrderStateValidator(UpdOrderStateEvent event) {
        Integer orderState = event.getOrderState();
        String orderSn = event.getOrderSn();
        if (PlusOrderStateEnum.PAY_SUCCESS.getCode() != orderState
                && PlusOrderStateEnum.CANCELED.getCode() != orderState) {
            throw new PlusAbyssException("只允许修改为【支付成功】或【取消】状态");
        }
        PlusOrderEntity order = plusOrderQueryModel.getByOrderSn(orderSn);
        ParamCheckUtils.checkNull(order, "无效的会员订单号，请确认后重试");
        if (PlusOrderStateEnum.PAY_SUCCESS.getCode() == orderState) {
            log.info("修改会员订单状态，支付成功逻辑校验：{}", orderSn);
            // 只有待支付的后付款订单才能改为支付成功
            if (PlusOrderStateEnum.WAIT_PAY.getCode() != order.getOrderState() || !Objects.equals(
                    PlusOrderPayTypeEnum.PAY_AFTER.getValue(), order.getPayType())) {
                throw new PlusAbyssException("无法变更请核实状态");
            }
            return order;
        }
        log.info("修改会员订单状态，取消逻辑校验：{}", orderSn);
        ParamCheckUtils.checkNull(event.getRefundAmount(), "请输入退款金额");
        // 只有支付成功的订单才能取消
        if (PlusOrderStateEnum.PAY_SUCCESS.getCode() != order.getOrderState()) {
            throw new PlusAbyssException("无法变更请核实状态");
        }
        if (event.getRefundAmount().compareTo(order.getOrderAmount()) > 0) {
            throw new PlusAbyssException("退款金额不能大于实付金额");
        }
        // 融担咨询卡不校验最后一笔订单
        if (JuziPlusEnum.RDZX_CARD.getCode() != order.getConfigId()) {
            log.info("非融担卡变更会员订单状态校验最后一笔订单开始：{}", orderSn);
            // 是否最后一笔有效订单
            List<MemberPlusInfoDetailEntity> maxDetailList = plusQueryModel.getDetailByUserId(
                    order.getUserId(), order.getConfigId());
            if (CollectionUtils.isNotEmpty(maxDetailList)) {
                MemberPlusInfoDetailEntity vo = maxDetailList.get(0);
                if (!orderSn.equals(vo.getOrderSn())) {
                    throw new PlusAbyssException(
                            "当前会员订单非最后一笔有效订单," + vo.getConfigName() + ":"
                                    + vo.getOrderSn());
                }
            }

        }
        return order;
    }

    /**
     * 查询会员订单列表参数校验
     */
    public void checkParam(PlusOrderInfoQueryReq req) {
        if (req.getPlusState() != null) {
            if (req.getOrderSn() == null && req.getOrderState() == null
                    && req.getOrderType() == null && req.getUserId() == null
                    && req.getMobile() == null && req.getConfigId() == null) {
                log.info("付费会员订单列表获取数据不能只选择会员状态：{}", JSONObject.toJSON(req));
                throw new PlusAbyssException("会员状态需与其他条件联合搜索");
            }
        }
        log.info("付费会员订单列表获取数据开始={}", JSONObject.toJSON(req));
        if (req.getMobile() != null) {
            if (req.getChannelId() == null) {
                throw new PlusAbyssException(String.valueOf(VipErrorEnum.ERROR_100002.getCode()),
                        "如需按手机号搜索，请先选择渠道");
            }
            MemberInfo member = memberExternalRepository.getMemberByMobile(req.getMobile(),
                    req.getChannelId());
            req.setUserId(member.getId());
        }
    }

    /**
     * 查询会员订单详情校验入参
     */
    public void checkQueryDetailParam(PlusOrderDetailQueryReq req) {
        ParamCheckUtils.checkNull(req, "参数不能为空");
        ParamCheckUtils.checkNull(req.getOrderSn(), "订单号不能为空");
    }

    /**
     * 取消订单申请校验
     */
    public PlusOrderEntity cancelOrderApplyValidator(PlusOrderCancelEvent event) {
        log.info("取消订单申请校验:{}", JSON.toJSONString(event));
        ParamCheckUtils.checkNull(event.getPlusOrderSn(), "会员单号不能为空");
        ParamCheckUtils.checkNull(event.getOptUserName(), "操作人姓名不能为空");
        ParamCheckUtils.checkNull(event.getOptUserId(), "操作人id不能为空");
        ParamCheckUtils.checkNull(event.getCancelType(), "取消方式不能为空");
        // 按比例取消，需要传入退款比例且校验比例是否正确
        if (event.getCancelType() == PlusCancelTypeEnum.RATIO.getValue() && (
                event.getRefundRate() == null
                        || event.getRefundRate().compareTo(BigDecimal.ZERO) < 0
                        || event.getRefundRate().compareTo(BigDecimal.ONE) > 0)) {
            throw new PlusAbyssException("退款比例不能为空且必须在0%-100%范围内");
        }
        // 防止重复发起原路退款
        String plusOrderSn = event.getPlusOrderSn();
        String redisKey = PlusConcatUtils.symbolBelowStr(
                RedisConstantPrefix.PLUS_ORDER_REFUND_APPLY, plusOrderSn);
        boolean lock = redisLock.lock(redisKey, "1", 3);
        if (!lock) {
            throw new PlusAbyssException("重复发起原路退款");
        }
        // 订单信息校验
        PlusOrderEntity plusOrderEntity = plusOrderQueryModel.getByOrderSn(event.getPlusOrderSn());
        Preconditions.checkNotNull(plusOrderEntity, "订单不存在");
        if (plusOrderEntity.getOrderState() == PlusOrderStateEnum.CANCELED.getCode()) {
            throw new PlusAbyssException("订单状态已取消,不可再次操作");
        }
        // 退款记录校验
        List<PlusOrderRefundInfoEntity> refundInfoList = orderRefundInfoModel.getByOrderSn(
                plusOrderSn, Arrays.asList(RefundInfoStateEnum.DOING.getCode(),
                        RefundInfoStateEnum.SUCCESS.getCode()));
        if (CollectionUtils.isNotEmpty(refundInfoList)) {
            throw new PlusAbyssException("订单存在已退款/退款中的退款记录,不可再次操作");
        }
        // 代付记录校验
        boolean hasOrder = orderDefrayModel.hasIngEnd(plusOrderSn);
        if (hasOrder) {
            throw new PlusAbyssException("当前订单存在进行中的代付记录,不可再次操作");
        }
        return plusOrderEntity;
    }

    /**
     * 订单中心退款通知校验
     */
    public void orderRefundNotifyValidator(OrderRefundNotifyEntity entity) {
        // 参数校验
        String thirdPayNum = entity.getThirdPayNum();
        String status = entity.getStatus();
        String plusOrderSn = entity.getOrderId();
        if (StringUtils.isAnyBlank(thirdPayNum, status, plusOrderSn)) {
            throw new PlusAbyssException("处理订单中心退款通知,参数为空");
        }
        // 退款类型
        Integer refundType = entity.getRefundType();
        if (refundType == null || PayRefundTypeEnum.getByCode(refundType) == null) {
            throw new PlusAbyssException("处理订单中心退款通知,退款类型为空/退款类型未知");
        }
        // 退款状态判断
        if (!RefundStateEnum.S.getCode().equals(status) && !RefundStateEnum.F.getCode()
                .equals(status)) {
            throw new PlusAbyssException("处理订单中心退款通知,退款状态未知");
        }
        String redisKey = PlusConcatUtils.symbolBelowStr(RedisConstantPrefix.PLUS_REFUND_LISTEN,
                thirdPayNum);
        boolean lock = redisLock.lock(redisKey, "1", 2);
        if (!lock) {
            throw new PlusAbyssException("处理订单中心退款通知,短时间通知多次,{}", thirdPayNum);
        }
    }

}
