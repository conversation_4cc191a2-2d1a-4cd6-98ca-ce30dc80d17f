package com.juzifenqi.plus.module.order.repository.dao;

import com.juzifenqi.plus.module.order.repository.po.PlusMonthMemberRenewalPlanPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 会员月卡续费计划Mapper
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
@Mapper
public interface IPlusMonthMemberRenewalPlanMapper {

    /**
     * 新增返回ID
     *
     * @param po 续费计划Po
     * @return 主键ID
     */
    Integer save(PlusMonthMemberRenewalPlanPo po);

    /**
     * 批量插入
     *
     * @param list 续费计划Po列表
     * @return 插入成功的记录数
     */
    Integer batchInsert(@Param("list") List<PlusMonthMemberRenewalPlanPo> list);

    /**
     * 根据订单号查询
     *
     * @param orderSn 会员订单号
     * @return 续费计划Po列表
     */
    List<PlusMonthMemberRenewalPlanPo> getByOrderSn(@Param("orderSn") String orderSn);

    /**
     * 根据订单号和状态查询
     *
     * @param orderSn 会员订单号
     * @param planStates 计划状态列表
     * @return 续费计划Po列表
     */
    List<PlusMonthMemberRenewalPlanPo> getByOrderSnAndStates(@Param("orderSn") String orderSn, 
                                                              @Param("planStates") List<Integer> planStates);

    /**
     * 分页查询续费计划记录
     *
     * @param planTime 计划生成时间
     * @param planStates 计划状态列表
     * @return 续费计划Po列表
     */
    List<PlusMonthMemberRenewalPlanPo> pageQuery(@Param("planTime") Date planTime, 
                                                  @Param("planStates") List<Integer> planStates);

    /**
     * 查询续费计划记录总数
     *
     * @param planTime 计划生成时间
     * @param planStates 计划状态列表
     * @return 记录总数
     */
    Integer countQuery(@Param("planTime") Date planTime, 
                       @Param("planStates") List<Integer> planStates);

    /**
     * 根据ID查询
     *
     * @param id 主键ID
     * @return 续费计划Po
     */
    PlusMonthMemberRenewalPlanPo getById(@Param("id") Integer id);

    /**
     * 更新计划状态
     *
     * @param id 主键ID
     * @param planState 计划状态
     * @param remark 备注
     * @return 更新记录数
     */
    Integer updatePlanState(@Param("id") Integer id,
                           @Param("planState") Integer planState,
                           @Param("remark") String remark);

    /**
     * 更新实际生成时间
     *
     * @param id 主键ID
     * @param actualPlanTime 实际生成时间
     * @return 更新记录数
     */
    Integer updateActualPlanTime(@Param("id") Integer id,
                                @Param("actualPlanTime") Date actualPlanTime);
}
