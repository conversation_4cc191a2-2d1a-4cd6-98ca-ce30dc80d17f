package com.juzifenqi.plus.module.market.api.impl;

import com.google.common.collect.Lists;
import com.juzifenqi.core.ServiceResult;
import com.juzifenqi.order.dto.OrderSimpleInfoDTO;
import com.juzifenqi.plus.api.IPlusMarketingLogApi;
import com.juzifenqi.plus.dto.resp.PlusAbyssResult;
import com.juzifenqi.plus.dto.resp.admin.common.PlusMarketingResultResp;
import com.juzifenqi.plus.dubbo.DubboService;
import com.juzifenqi.plus.enums.JuziPlusEnum;
import com.juzifenqi.plus.external.OrderExternal;
import com.juzifenqi.plus.module.common.IPlusMarketingRepository;
import com.juzifenqi.plus.module.common.repository.po.PlusMarketLogPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 会员营销日志查询服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/25 15:24
 *
 */
@Slf4j
@DubboService
public class PlusMarketingLogApiImpl implements IPlusMarketingLogApi {


    @Resource
    private IPlusMarketingRepository marketingLogRepository;


    @Resource
    private OrderExternal orderExternal;

    private static final List<Integer> RAISE_AMOUNT_CARDS = Lists.newArrayList(
            JuziPlusEnum.NEW_JUXP_CARD.getCode(), JuziPlusEnum.QUOTA_CARD.getCode(), JuziPlusEnum.YITONG_CARD.getCode(),
            JuziPlusEnum.HYYK_CARD.getCode()
    );

    /**
     * 根据订单id查询营销日志
     * @param orderId 订单id
     * @return
     */
    @Override
    public PlusAbyssResult<PlusMarketingResultResp> getMarketLogByOrderId(String orderId) {
        if (StringUtils.isBlank(orderId)) {
            return PlusAbyssResult.error("借款订单号不能为空");
        }
        PlusMarketingResultResp resp = new PlusMarketingResultResp();
        resp.setMarketExpediteCard(false);
        resp.setMarketRaiseAmountCard(false);
        ServiceResult<OrderSimpleInfoDTO> orderResult = orderExternal.getCommonDataBySn(orderId);
        if (orderResult.getSuccess() && orderResult.getResult() != null) {
            OrderSimpleInfoDTO orderInfo = orderResult.getResult();
            Date orderCreateTime = orderInfo.getCreateTime();
            Integer userId = orderInfo.getMemberId();
            PlusMarketLogPo marketLog = marketingLogRepository.getMarketLog(userId, orderCreateTime);
            if (marketLog != null) {
                if (RAISE_AMOUNT_CARDS.contains(marketLog.getConfigId())) {
                    resp.setMarketRaiseAmountCard(true);
                }
                if (Objects.equals(marketLog.getConfigId(), JuziPlusEnum.EXPEDITE_CARD.getCode())) {
                    resp.setMarketExpediteCard(true);
                }
            }
        }
        return PlusAbyssResult.success(resp);
    }

    @Override
    public void marketLogMigrate(Integer days, Integer limit) {
        marketingLogRepository.migrateMarketLog(days, limit);
    }
}
