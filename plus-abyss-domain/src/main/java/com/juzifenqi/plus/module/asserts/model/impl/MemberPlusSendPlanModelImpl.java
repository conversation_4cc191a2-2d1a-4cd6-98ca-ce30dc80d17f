package com.juzifenqi.plus.module.asserts.model.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.groot.utils.core.date.DateUtils;
import com.groot.utils.exception.LogUtil;
import com.juzifenqi.enumeration.OrderChannelEnum;
import com.juzifenqi.plus.constants.PlusConstant;
import com.juzifenqi.plus.enums.ChannelEnum;
import com.juzifenqi.plus.enums.JuziPlusEnum;
import com.juzifenqi.plus.enums.PlusModelEnum;
import com.juzifenqi.plus.enums.PlusOrderStateEnum;
import com.juzifenqi.plus.enums.PlusProfitsGroupEnum;
import com.juzifenqi.plus.enums.PlusProgramSendTypeEnum;
import com.juzifenqi.plus.enums.PlusSendPlanStatusEnum;
import com.juzifenqi.plus.enums.PlusSendPlanTaskStatusEnum;
import com.juzifenqi.plus.exception.PlusAbyssException;
import com.juzifenqi.plus.module.asserts.application.IMemberPlusSendPlanApplication;
import com.juzifenqi.plus.module.asserts.model.MemberPlusQueryModel;
import com.juzifenqi.plus.module.asserts.model.MemberPlusSendPlanModel;
import com.juzifenqi.plus.module.asserts.model.contract.IMemberPlusCouponRepository;
import com.juzifenqi.plus.module.asserts.model.contract.IMemberPlusSendPlanRepository;
import com.juzifenqi.plus.module.asserts.model.contract.IMemberPlusSendPlanTaskRepository;
import com.juzifenqi.plus.module.asserts.model.contract.entity.ExpireMemberPlusSendPlanEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusDetailPeriodsEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusInfoEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusSendPlanConditionEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusSendPlanEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusSendPlanTaskEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.PlusMemberCardEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.SendPlanTimeEntity;
import com.juzifenqi.plus.module.asserts.model.converter.IPlusMemberProfitsSendPlanModelConverter;
import com.juzifenqi.plus.module.asserts.model.event.HandleProfitsEvent;
import com.juzifenqi.plus.module.asserts.model.event.LoanSucProfitsEvent;
import com.juzifenqi.plus.module.asserts.model.event.MemberPlusSendPlanConditionEvent;
import com.juzifenqi.plus.module.asserts.model.event.MemberPlusSendPlanCreateEvent;
import com.juzifenqi.plus.module.asserts.model.event.MemberPlusSendPlanResetEvent;
import com.juzifenqi.plus.module.asserts.model.event.OrderProfitsEvent;
import com.juzifenqi.plus.module.asserts.model.event.PlusProfitSendPlanEvent;
import com.juzifenqi.plus.module.asserts.model.event.plan.condition.DmdsTaskFinishEvent;
import com.juzifenqi.plus.module.asserts.model.event.plan.condition.GwfxFinishEvent;
import com.juzifenqi.plus.module.asserts.model.event.plan.condition.JJpTaskFinishEvent;
import com.juzifenqi.plus.module.asserts.model.event.plan.condition.OrderReceiveProfitsEvent;
import com.juzifenqi.plus.module.asserts.model.impl.strategy.plan.SendPlanConditionFactory;
import com.juzifenqi.plus.module.asserts.model.impl.strategy.plus.ExpeditePlusStrategy;
import com.juzifenqi.plus.module.asserts.model.impl.strategy.plus.StrategyHandlerContext;
import com.juzifenqi.plus.module.asserts.model.impl.strategy.profits.ProfitHandlerContext;
import com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponDmdsPo;
import com.juzifenqi.plus.module.common.IAuthExternalRepository;
import com.juzifenqi.plus.module.order.model.PlusOrderSnapshtoQueryModel;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderRepository;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramProfitsCouponObject;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramProfitsPackageObject;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramProfitsVirtualObject;
import com.jzfq.auth.core.api.entiy.AuthIdentityDetail;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MemberPlusSendPlanModelImpl implements MemberPlusSendPlanModel {

    @Autowired
    private IMemberPlusSendPlanRepository     memberPlusSendPlanRepository;
    @Autowired
    private SendPlanConditionFactory          sendPlanConditionFactory;
    @Autowired
    private MemberPlusQueryModel              plusQueryModel;
    @Autowired
    private IMemberPlusCouponRepository       couponRepository;
    @Autowired
    private StrategyHandlerContext            handlerContext;
    @Autowired
    private ProfitHandlerContext              profitHandlerContext;
    @Autowired
    private IAuthExternalRepository           authRepository;
    @Autowired
    private IMemberPlusSendPlanTaskRepository memberPlusSendPlanTaskRepository;
    @Autowired
    private IPlusOrderRepository              plusOrderRepository;
    @Autowired
    private IMemberPlusSendPlanApplication    memberPlusSendPlanApplication;
    @Autowired
    private PlusOrderSnapshtoQueryModel       plusOrderSnapshtoQueryModel;
    @Autowired
    private ExpeditePlusStrategy    expeditePlusStrategy;

    private final IPlusMemberProfitsSendPlanModelConverter converter = IPlusMemberProfitsSendPlanModelConverter.instance;


    /**
     * 1. 生成发放计划
     */
    @Override
    public void generateMemberProfitsSendPlan(
            MemberPlusSendPlanCreateEvent memberPlusSendPlanCreateEvent) {
        try {
            PlusMemberCardEntity memberCard = memberPlusSendPlanCreateEvent.getPlusMemberCard();
            List<PlusProgramProfitsPackageObject> programProfitsPackages = memberPlusSendPlanCreateEvent.getProgramEntity()
                    .getProfitsPackageList();
            programProfitsPackages.forEach(
                    programProfitsPackage -> generateMemberProfitsSendPlan(programProfitsPackage,
                            memberCard));
        } catch (Exception e) {
            log.error("生成发放计划失败", e);
        }
    }

    @Override
    public void generateMemberProfitsSendPlanByModel(MemberPlusSendPlanResetEvent resetEvent) {
        try {
            PlusMemberCardEntity memberCard = resetEvent.getPlusMemberCard();
            List<PlusProgramProfitsPackageObject> programProfitsPackages = resetEvent.getProgramEntity()
                    .getProfitsPackageList();
            programProfitsPackages.forEach(programProfitsPackage -> {
                Integer modelId = resetEvent.getModelId();
                if (!Objects.equals(programProfitsPackage.getModelId(), modelId)) {
                    return;
                }
                String orderSn = resetEvent.getPlusMemberCard().getOrderSn();
                // 删除原有的发放计划和发放任务
                memberPlusSendPlanRepository.delSendPlan(orderSn, modelId);
                // 删除原member_coupon开头的老表数据
                couponRepository.delCouponByModel(orderSn, modelId);
                // 重新生成新的发放计划
                generateMemberProfitsSendPlan(programProfitsPackage, memberCard);
                // 对于后付款发放节点，开卡礼、拒就赔、多买多送、月享红包 初始化状态为未生效，需要修改任务状态
                refreshProfitSendPlan(memberCard, modelId);
            });
        } catch (Exception e) {
            log.error("重新生成发放计划失败", e);
        }
    }

    private void refreshProfitSendPlan(PlusMemberCardEntity memberCard, Integer modelId) {
        if (!PlusConstant.MERGE_CARD_LIST.contains(memberCard.getConfigId())) {
            log.info("非会员合并卡，无需重新处理计划状态 memberCard {}", JSONObject.toJSON(memberCard));
            return;
        }
        PlusOrderEntity plusOrderEntity = plusOrderRepository.getByPlusOrderSn(
                memberCard.getOrderSn());
        if (Objects.equals(PlusOrderStateEnum.PAY_SUCCESS.getCode(),
                plusOrderEntity.getOrderState())) {
            // 刷新权益计划列表，补增计划任务-开卡礼、月享红包、多买多送、拒就赔
            PlusProfitSendPlanEvent event = converter.toPlusProfitSendPlanEvent(
                    memberCard.getOrderSn(), memberCard.getProgramId(), memberCard.getConfigId(),
                    Arrays.asList(modelId));
            memberPlusSendPlanApplication.refreshProfitSendPlan(event);
        }
    }

    @Override
    public List<Integer> generateMemberProfitsSendPlanByPlan(List<MemberPlusSendPlanEntity> list) {
        try {
            if (CollectionUtils.isEmpty(list)) {
                return null;
            }
            List<Integer> sendPlanIdList = new ArrayList<>(list.size());
            for (MemberPlusSendPlanEntity memberPlusSendPlanEntity : list) {
                Integer sendPlanId = memberPlusSendPlanRepository.save(memberPlusSendPlanEntity);
                sendPlanIdList.add(sendPlanId);
            }
            return sendPlanIdList;
        } catch (Exception e) {
            log.error("生成指定发放计划失败", e);
        }
        return null;
    }

    /**
     * 2. 取消
     */
    @Override
    public void cancelMemberProfitsSendPlan(Long userId, Long plusOrderId) {
    }

    /**
     * 3.1 任务达成处理
     */
    @Override
    public List<Integer> doPlan(MemberPlusSendPlanConditionEvent orderPayFinishEvent) {
        List<MemberPlusSendPlanConditionEntity> conditions = sendPlanConditionFactory.doExecute(
                orderPayFinishEvent);
        return doCommonPlan(orderPayFinishEvent, conditions);
    }

    /**
     * 达成条件公共处理逻辑
     */
    private List<Integer> doCommonPlan(MemberPlusSendPlanConditionEvent conditionEvent,
            List<MemberPlusSendPlanConditionEntity> conditions) {

        // 4. 更新发放计划条件达成
        if (CollectionUtils.isEmpty(conditions)) {
            return Collections.emptyList();
        }
        List<Integer> needHandleSendPlanIds = new ArrayList<>(conditions.size());
        for (MemberPlusSendPlanConditionEntity condition : conditions) {
            Boolean isReached = conditionEvent.getConditionType()
                    .isReached(condition.getReachCondition(), condition.getReachValue());
            condition.setIsReached(isReached);
            memberPlusSendPlanRepository.updateCondition(condition);
            log.info("发放计划任务达成处理，isReached = {}, event = {}", isReached,
                    JSON.toJSONString(condition));
            if (isReached && condition.getIsTaskTrigger()) {
                needHandleSendPlanIds.add(condition.getMemberPlusSendPlanId());
            }
        }
        // 5. 返回需要触发全局发放计划变更的id
        return needHandleSendPlanIds;
    }

    /**
     * 发放权益
     */
    @Override
    public void updateStatus(Integer sendPlanId, Integer sendStatus) {
        // 更新权益发放计划为已发放
        memberPlusSendPlanRepository.updateSendStatus(sendPlanId, sendStatus);
    }

    @Override
    public void dmdsTaskFinishEvent(DmdsTaskFinishEvent event, MemberPlusInfoEntity plusInfo) {
        try {
            log.info("老多买多送任务达成处理逻辑开始：{},{},{}", event.getUserId(), event.getOrderSn(),
                    plusInfo.getConfigId());
            Integer userId = event.getUserId();
            Integer channelId = event.getChannelId();
            // 803没有商品单，无需更改
            if (!Objects.equals(ChannelEnum.MALL.getCode(), event.getChannelId())
                    || !Objects.equals(OrderChannelEnum.商品订单.getChannelCode(),
                    event.getOrderChannelId())) {
                log.info("当前订单不是商品单或商城用户,不处理多买多送：{},{}", userId, channelId);
                return;
            }
            MemberCouponDmdsPo po = couponRepository.getMinOrdersNumOfCurrentCoupon(userId,
                    plusInfo.getConfigId());
            if (po == null) {
                log.info("未获取到需要修改的优惠券配置：{},{}", plusInfo.getConfigId(), plusInfo.getUserId());
                return;
            }
            // 未配置需要达成的订单金额 或 配置的订单金额<=下单金额则满足达成条件
            if (po.getOrderMoney() == null
                    || po.getOrderMoney().compareTo(event.getOrderAmount()) <= 0) {
                String bak = po.getOrderMoney() == null ? "未设置订单金额,直接达成多买多送任务"
                        : "订单金额>=设置的下单金额,达成多买多送任务";
                couponRepository.dmdsTaskFinished(po, bak);
            }
            log.info("老多买多送任务达成处理逻辑结束：{},{},{}", event.getUserId(), event.getOrderSn(),
                    plusInfo.getConfigId());
        } catch (Exception e) {
            LogUtil.printLog(e, "老多买多送任务达成处理逻辑异常");
        }
    }

    @Override
    public void jjpTaskFinishEvent(JJpTaskFinishEvent event, MemberPlusInfoEntity plusInfo) {
        try {
            log.info("老拒就赔任务达成处理逻辑开始：{},{},{}", event.getUserId(), plusInfo.getConfigId(),
                    event.getOrderSn());
            couponRepository.jjpTaskFinished(plusInfo, event);
            log.info("老拒就赔任务达成处理逻辑完成：{},{},{}", event.getUserId(), plusInfo.getConfigId(),
                    event.getOrderSn());
        } catch (Exception e) {
            LogUtil.printLog(e, "老拒就赔任务达成处理异常");
        }
    }

    @Override
    public void gwfxTaskFinishEvent(GwfxFinishEvent event, MemberPlusInfoEntity plusInfo) {
        try {
            log.info("老购物返现处理开始：{}", JSON.toJSONString(event));
            // 803没有商品单
            if (!Objects.equals(ChannelEnum.MALL.getCode(), event.getChannelId())
                    || !Objects.equals(OrderChannelEnum.商品订单.getChannelCode(),
                    event.getOrderChannelId()) || 0 != event.getIsFullPayment()) {
                log.info("商品单信用支付成功处理购物返现非商城用户和分期商品单：{}", event.getOrderSn());
                return;
            }
            HandleProfitsEvent payProfitsEvent = converter.toHandleProfitsEvent(event, plusInfo);
            handlerContext.doCreditPayProfits(payProfitsEvent);
            log.info("老购物返现处理完成：{},{}", event.getUserId(), event.getOrderSn());
        } catch (Exception e) {
            LogUtil.printLog(e, "商品单信用支付成功处理购物返现异常");
        }
    }

    @Override
    public void profitsCancelEvent(OrderProfitsEvent event) {
        try {
            log.info("商品单取消成功处理权益回退开始：{}", JSON.toJSONString(event));
            // 803没有商品单
            if (!Objects.equals(ChannelEnum.MALL.getCode(), event.getChannelId())
                    || !Objects.equals(OrderChannelEnum.商品订单.getChannelCode(),
                    event.getOrderChannelId())) {
                log.info("商品单取消成功处理权益回退非商城用户和商品单：{}", event.getOrderSn());
                return;
            }
            Integer userId = event.getUserId();
            Integer channelId = event.getChannelId();
            List<MemberPlusInfoEntity> memberPlusInfoList = plusQueryModel.getMemberPlusInfoList(
                    userId, channelId);
            if (CollectionUtils.isEmpty(memberPlusInfoList)) {
                log.info("商品单取消成功处理权益回退非会员身份：{},{}", event.getOrderSn(), userId);
                return;
            }
            for (MemberPlusInfoEntity entity : memberPlusInfoList) {
                HandleProfitsEvent payProfitsEvent = converter.toHandleProfitsEvent(event, entity,
                        memberPlusInfoList);
                handlerContext.doCancelProfits(payProfitsEvent);
            }
            log.info("商品单取消成功处理权益回退完成：{},{}", userId, event.getOrderSn());
        } catch (Exception e) {
            LogUtil.printLog(e, "商城单取消成功权益回退处理异常");
        }
    }

    @Override
    public void productOrderProfitsEvent(OrderProfitsEvent event) {
        try {
            log.info("商品单下单成功权益处理开始：{}", JSON.toJSONString(event));
            // 803没有商品单
            if (!Objects.equals(ChannelEnum.MALL.getCode(), event.getChannelId())
                    || !Objects.equals(OrderChannelEnum.商品订单.getChannelCode(),
                    event.getOrderChannelId())) {
                log.info("商品单下单成功权益处理非商城用户和商品单：{}", event.getOrderSn());
                return;
            }
            Integer userId = event.getUserId();
            Integer channelId = event.getChannelId();
            List<MemberPlusInfoEntity> memberPlusInfoList = plusQueryModel.getMemberPlusInfoList(
                    userId, channelId);
            if (CollectionUtils.isEmpty(memberPlusInfoList)) {
                log.info("商品单下单成功权益处理非商城非会员身份：{},{}", event.getOrderSn(), userId);
                return;
            }
            for (MemberPlusInfoEntity entity : memberPlusInfoList) {
                HandleProfitsEvent payProfitsEvent = converter.toHandleProfitsEvent(event, entity,
                        memberPlusInfoList);
                handlerContext.doUseProfits(payProfitsEvent);
            }
            log.info("商品单下单成功权益处理完成：{}", event.getOrderSn());
        } catch (Exception e) {
            LogUtil.printLog(e, "商品单下单成功权益处理异常");
        }
    }

    @Override
    public void loanSucProfitEvent(LoanSucProfitsEvent event) {
        log.info("借款单放款成功专属权益处理开始：{}", JSON.toJSONString(event));
        if (event.getLoanOrderCreateTime() == null) {
            log.info(
                    "借款单放款成功专属权益处理... 借款单无创单时间不处理, userId={},loadOrderSn={},channelId={}",
                    event.getUserId(), event.getLoanOrderSn(), event.getChannelId());
            return;
        }
        String loanOrderSn = event.getLoanOrderSn();
        Integer userId = event.getUserId();
        List<MemberPlusInfoEntity> memberPlusInfoList = plusQueryModel.getMemberPlusInfoList(userId,
                event.getChannelId());
        if (CollectionUtils.isEmpty(memberPlusInfoList)) {
            log.info("借款单放款成功专属权益处理非会员身份：{},{}", userId, loanOrderSn);
            return;
        }
        List<MemberPlusInfoEntity> collect = memberPlusInfoList.stream()
                .filter(plusInfo -> Arrays.asList(JuziPlusEnum.NEW_JUXP_CARD.getCode(),
                                JuziPlusEnum.QUOTA_CARD.getCode(), JuziPlusEnum.YITONG_CARD.getCode(), JuziPlusEnum.HYYK_CARD.getCode(), JuziPlusEnum.EXPEDITE_CARD.getCode())
                        .contains(plusInfo.getConfigId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            log.info(
                    "借款单放款成功专属权益处理... 非桔享卡、提额卡、加速卡不处理, userId={},channelId={},loadOrderSn={}",
                    userId, event.getChannelId(), loanOrderSn);
            return;
        }
        collect.stream().filter(plusInfo -> Objects.equals(JuziPlusEnum.EXPEDITE_CARD.getCode(),
                        plusInfo.getConfigId())).findAny()
                .ifPresent(entity -> event.setConfigId(entity.getConfigId()));
        // 统一使用加速卡策略处理
        expeditePlusStrategy.doLoanSucProfit(event);
        log.info("借款单放款成功专属权益处理完成：{},{}", userId, loanOrderSn);
    }


    @Override
    public void orderReceiveProfitsEvent(OrderReceiveProfitsEvent event) {
        log.info("商品单收货处理开始：{}", JSON.toJSONString(event));
        try {
            String orderSn = event.getOrderSn();
            Integer userId = event.getUserId();
            HandleProfitsEvent profitsEvent = converter.toHandleProfitsEvent(event);
            for (Integer modelId : PlusModelEnum.RECEIVE_MODEL_IDS) {
                profitsEvent.setModelId(modelId);
                profitHandlerContext.doOrderReceiveProfits(profitsEvent);
            }
            log.info("商品单收货处理完成：{},{}", orderSn, userId);
        } catch (Exception e) {
            LogUtil.printLog(e, "商品单收货处理异常");
        }
    }

    @Override
    public List<MemberPlusSendPlanEntity> getSendPlanByOrderSn(String orderSn) {
        // 正常发放计划没有，查询失效发放计划
        List<MemberPlusSendPlanEntity> list = memberPlusSendPlanRepository.listByOrderSn(orderSn);
        if (CollectionUtils.isEmpty(list)) {
            List<ExpireMemberPlusSendPlanEntity> expireList = memberPlusSendPlanRepository.listExpireByOrderSn(
                    orderSn);
            if (CollectionUtils.isEmpty(expireList)) {
                return null;
            }
            return converter.toMemberPlusSendPlanEntityList(expireList);
        }
        return list;
    }

    /**
     * 生成发放计划
     */
    private void generateMemberProfitsSendPlan(PlusProgramProfitsPackageObject profitsPackage,
            PlusMemberCardEntity plusMemberCard) {
        switch (profitsPackage.getGivePeriod()) {
            case ONCE_IMMEDIATELY: {
                Date startTime = new Date();
                generateMemberProfitsSendPlan(profitsPackage, startTime, null, plusMemberCard);
                break;
            }
            case ONCE_MONTH: {
                // 场景：不同的权益可能开始时间或结束时间不一样。如：月享红包是开始时间第二天开始发。多买多送开始时间+1,结束时间也+1
                List<SendPlanTimeEntity> sendPlanTime = generateSendPlanTimeByOneMonth(
                        profitsPackage, plusMemberCard);
                for (SendPlanTimeEntity entity : sendPlanTime) {
                    generateMemberProfitsSendPlan(profitsPackage, entity.getStartTime(),
                            entity.getEndTime(), plusMemberCard);
                }
                break;
            }
            case ONCE_PERIODS: {
                SendPlanTimeEntity entity = generateSendPlanTimeByOnePeriods(profitsPackage,
                        plusMemberCard);
                if (entity == null) {
                    log.error("生成发放计划时间失败：{}", plusMemberCard.getOrderSn());
                    return;
                }
                generateMemberProfitsSendPlan(profitsPackage, entity.getStartTime(),
                        entity.getEndTime(), plusMemberCard);
                break;
            }
            case ONCE_PERIODS_2: {
                Date startTime = new Date();
                Date endTime = plusMemberCard.getEndTime();
                generateMemberProfitsSendPlan(profitsPackage, startTime, endTime, plusMemberCard);
                break;
            }
            default:
                throw new PlusAbyssException("", "暂不支持此种发放周期");
        }
    }

    /**
     * 生成发放计划
     */
    private void generateMemberProfitsSendPlan(PlusProgramProfitsPackageObject profitsPackage,
            Date startTime, Date endTime, PlusMemberCardEntity plusMemberCard) {
        // 1. 生成券的发放计划
        generateCouponSendPlan(profitsPackage, startTime, endTime, plusMemberCard);
        // 2. 生成额度的发放计划
        generateLiftAmountSendPlan(profitsPackage, startTime, endTime, plusMemberCard);
        // 3. 生成返现的发放计划
        generateCashbackSendPlan(profitsPackage, startTime, endTime, plusMemberCard);
        // 4. 生成虚拟货币的发放计划（目前是桔豆）
        generateVirtualSendPlan(profitsPackage, startTime, endTime, plusMemberCard);
        // 5. 生成虚拟商品的发放计划（目前是权益0元发放和联名卡虚拟权益）
        generateVirtualGoodsSendPlan(profitsPackage, startTime, endTime, plusMemberCard);
    }

    private void generateCouponSendPlan(PlusProgramProfitsPackageObject profitsPackage,
            Date startTime, Date endTime, PlusMemberCardEntity plusMemberCard) {
        // 发券
        if (!CollectionUtils.isEmpty(profitsPackage.getCoupons())) {
            log.info("生成券的发放计划，data = {}", JSON.toJSONString(profitsPackage.getCoupons()));
            profitsPackage.getCoupons().forEach(coupon -> {
                // 不同的权益（券），发放状态不一样，要做处理
                Integer sendState = initSendState(profitsPackage.getModelId(),
                        profitsPackage.getSendNode());
                // 不同的权益（券），可能存在不同的发放任务，要做处理
                MemberPlusSendPlanTaskEntity task = initPlusSendPlanTask(profitsPackage, startTime,
                        plusMemberCard, coupon);
                MemberPlusSendPlanEntity memberPlusSendPlanEntity = converter.toMemberProfitsSendPlanEntity(
                        coupon, profitsPackage, startTime, endTime, sendState, plusMemberCard,
                        PlusProfitsGroupEnum.PLUS_COUPON.getValue(), task);
                // 保存
                memberPlusSendPlanRepository.save(memberPlusSendPlanEntity);
            });
        }
    }

    /**
     * 获取生成发放计划的发放状态
     * <p>购物返现：需要购买商品达成任务</p>
     * <p>拒就赔：需要借款/商品单风控拒绝达成任务</p>
     * <p>生日关怀：需要到生日那天发发放，达成</p>
     * <p>多买多送：需要购买商品达成任务</p>
     * <p>品牌专区：需要task任务</p>
     * <p>月享红包：需要task任务</p>
     * <p>还款优化：已就绪，用户手工领取</p>
     */
    private Integer initSendState(Integer modelId, Integer sendNode) {
        PlusModelEnum plusModelEnum = PlusModelEnum.getPlusModelEnum(modelId);
        switch (plusModelEnum) {
            case GWFX:
            case SRGH:
            case PPZQ:
                return PlusSendPlanStatusEnum.CONDITIONAL.getValue();
            case KKL:
                return PlusProgramSendTypeEnum.AFTER_PAY.getValue().equals(sendNode)
                        ? PlusSendPlanStatusEnum.NOT_EFFECTIVE.getValue()
                        : PlusSendPlanStatusEnum.READY.getValue();
            case DMDS:
            case JJP:
            case YX:// 后付款支付成功发放 - 初始化状态为 未生效
                return PlusProgramSendTypeEnum.AFTER_PAY.getValue().equals(sendNode)
                        ? PlusSendPlanStatusEnum.NOT_EFFECTIVE.getValue()
                        : PlusSendPlanStatusEnum.CONDITIONAL.getValue();
            default:
                return PlusSendPlanStatusEnum.READY.getValue();
        }
    }

    /**
     * 获取虚拟货币：生成发放计划的发放状态
     */
    private Integer initVirtualSendState(Integer modelId) {
        PlusModelEnum plusModelEnum = PlusModelEnum.getPlusModelEnum(modelId);
        switch (plusModelEnum) {
            // 目前只有生日关怀有桔豆
            case SRGH:
                return PlusSendPlanStatusEnum.CONDITIONAL.getValue();
            default:
                return PlusSendPlanStatusEnum.READY.getValue();
        }
    }

    /**
     * 生成发放计划任务
     *
     * <AUTHOR>
     * @version 1.0
     * @date 2024/2/27 11:14
     */
    private MemberPlusSendPlanTaskEntity initPlusSendPlanTask(
            PlusProgramProfitsPackageObject profitsPackage, Date startTime,
            PlusMemberCardEntity plusMemberCard, PlusProgramProfitsCouponObject coupon) {
        PlusModelEnum plusModelEnum = PlusModelEnum.getPlusModelEnum(profitsPackage.getModelId());
        switch (plusModelEnum) {
            case SRGH:
            case PPZQ:
                return converter.toMemberProfitsSendPlanTaskEntity(coupon, profitsPackage,
                        startTime, plusMemberCard, PlusProfitsGroupEnum.PLUS_COUPON.getValue(),
                        PlusSendPlanTaskStatusEnum.WAIT_GRANT.getValue());
            case YX://月享红包-后付款支付成功发放-暂不生成发放计划
                return PlusProgramSendTypeEnum.AFTER_PAY.getValue()
                        .equals(profitsPackage.getSendNode()) ? null
                        : converter.toMemberProfitsSendPlanTaskEntity(coupon, profitsPackage,
                                startTime, plusMemberCard,
                                PlusProfitsGroupEnum.PLUS_COUPON.getValue(),
                                PlusSendPlanTaskStatusEnum.WAIT_GRANT.getValue());
            default:
                return null;
        }
    }

    /**
     * 生成虚拟货币：发放计划任务
     */
    private MemberPlusSendPlanTaskEntity initVirtualPlusSendPlanTask(
            PlusProgramProfitsPackageObject profitsPackage, Date startTime,
            PlusMemberCardEntity plusMemberCard, PlusProgramProfitsVirtualObject virtual) {
        PlusModelEnum plusModelEnum = PlusModelEnum.getPlusModelEnum(profitsPackage.getModelId());
        switch (plusModelEnum) {
            // 目前只有生日关怀有桔豆
            case SRGH:
                return converter.toMemberProfitsSendPlanTaskEntity(virtual, profitsPackage,
                        startTime, plusMemberCard, PlusProfitsGroupEnum.PLUS_COUPON.getValue(),
                        PlusSendPlanTaskStatusEnum.WAIT_GRANT.getValue());
            default:
                return null;
        }
    }

    private void generateLiftAmountSendPlan(PlusProgramProfitsPackageObject profitsPackage,
            Date startTime, Date endTime, PlusMemberCardEntity plusMemberCard) {
        // 发额度
        if (!CollectionUtils.isEmpty(profitsPackage.getPlusCredits())) {
            log.info("生成提额的发放计划，data = {}", JSON.toJSONString(profitsPackage.getPlusCredits()));
            profitsPackage.getPlusCredits().forEach(liftAmount -> {
                MemberPlusSendPlanEntity memberPlusSendPlanEntity = converter.toMemberProfitsSendPlanEntity(
                        liftAmount, profitsPackage, startTime, endTime,
                        PlusSendPlanStatusEnum.READY.getValue(), plusMemberCard,
                        PlusProfitsGroupEnum.PLUS_CREDIT.getValue());
                // 保存
                memberPlusSendPlanRepository.save(memberPlusSendPlanEntity);
            });
        }
    }

    /**
     * 生成发放计划和达成条件
     */
    private void generateCashbackSendPlan(PlusProgramProfitsPackageObject profitsPackage,
            Date startTime, Date endTime, PlusMemberCardEntity plusMemberCard) {
        // 发返现
        if (!CollectionUtils.isEmpty(profitsPackage.getPlusCashBacks())) {
            log.info("生成返现的发放计划，data = {}", JSON.toJSONString(profitsPackage.getPlusCashBacks()));
            profitsPackage.getPlusCashBacks().forEach(cashback -> {
                MemberPlusSendPlanEntity memberPlusSendPlanEntity = converter.toMemberProfitsSendPlanEntity(
                        cashback, profitsPackage, startTime, endTime,
                        PlusSendPlanStatusEnum.CONDITIONAL.getValue(), plusMemberCard,
                        PlusProfitsGroupEnum.PLUS_CASH.getValue());
                // 保存
                memberPlusSendPlanRepository.save(memberPlusSendPlanEntity);
            });
        }
    }

    /**
     * 生成虚拟货币的发放计划
     */
    private void generateVirtualSendPlan(PlusProgramProfitsPackageObject profitsPackage,
            Date startTime, Date endTime, PlusMemberCardEntity plusMemberCard) {
        // 发返现
        if (!CollectionUtils.isEmpty(profitsPackage.getVirtual())) {
            log.info("生成虚拟货币的发放计划，data = {}", JSON.toJSONString(profitsPackage.getVirtual()));
            profitsPackage.getVirtual().forEach(virtual -> {
                // 不同的权益，发放状态不一样，要做处理
                Integer sendState = initVirtualSendState(profitsPackage.getModelId());
                // 生成发放计划任务
                MemberPlusSendPlanTaskEntity task = initVirtualPlusSendPlanTask(profitsPackage,
                        startTime, plusMemberCard, virtual);
                MemberPlusSendPlanEntity memberPlusSendPlanEntity = converter.toMemberProfitsSendPlanEntity(
                        virtual, profitsPackage, startTime, endTime, sendState, plusMemberCard,
                        PlusProfitsGroupEnum.PLUS_VIRTUAL.getValue(), task);
                // 保存
                memberPlusSendPlanRepository.save(memberPlusSendPlanEntity);
            });
        }
    }

    /**
     * 生成虚拟商品的发放计划
     */
    private void generateVirtualGoodsSendPlan(PlusProgramProfitsPackageObject profitsPackage,
            Date startTime, Date endTime, PlusMemberCardEntity plusMemberCard) {
        if (!CollectionUtils.isEmpty(profitsPackage.getVirtualGoods())) {
            log.info("生成虚拟商品的发放计划，data = {}", JSON.toJSONString(profitsPackage.getVirtualGoods()));
            profitsPackage.getVirtualGoods().forEach(virtualGoods -> {
                MemberPlusSendPlanEntity memberPlusSendPlanEntity = converter.toMemberProfitsSendPlanEntity(
                        virtualGoods, profitsPackage, startTime, endTime,
                        PlusSendPlanStatusEnum.READY.getValue(), plusMemberCard,
                        PlusProfitsGroupEnum.PLUS_PRODUCT_VIRTUAL.getValue());
                // 保存
                memberPlusSendPlanRepository.save(memberPlusSendPlanEntity);
            });
        }
    }

    /**
     * 计算发放计划的开始时间和结束时间（类型：每月一次）
     */
    private List<SendPlanTimeEntity> generateSendPlanTimeByOneMonth(
            PlusProgramProfitsPackageObject packageObject, PlusMemberCardEntity card) {
        log.info("计算发放计划的开始时间和结束时间开始：{},{}", card.getOrderSn(), packageObject.getModelId());
        List<MemberPlusDetailPeriodsEntity> periods = plusQueryModel.getPeriods(card.getOrderSn());
        if (CollectionUtils.isEmpty(periods)) {
            throw new PlusAbyssException("未获取到会员周期段信息");
        }
        List<SendPlanTimeEntity> sendPlanTime = converter.toSendPlanTime(periods);
        Integer modelId = packageObject.getModelId();
        PlusModelEnum plusModelEnum = PlusModelEnum.getPlusModelEnum(modelId);
        switch (plusModelEnum) {
            //            case JJP:
            //            case DMDS:
            case PPZQ:
//            case YX:
                sendPlanTime.forEach(e -> {
                    e.setStartTime(DateUtils.addDays(e.getStartTime(), 1));
                    e.setEndTime(DateUtils.addDays(e.getEndTime(), 1));
                });
                break;
            default:
                log.info("计算发放计划的开始时间和结束时间非处理权益：{}", modelId);
                break;
        }
        log.info("计算发放计划的开始时间和结束时间完成：{}", JSON.toJSONString(sendPlanTime));
        return sendPlanTime;
    }

    /**
     * 生成发放计划时间（类型：全周期一次）
     */
    private SendPlanTimeEntity generateSendPlanTimeByOnePeriods(
            PlusProgramProfitsPackageObject packageObject, PlusMemberCardEntity card) {
        SendPlanTimeEntity entity = new SendPlanTimeEntity();
        Integer modelId = packageObject.getModelId();
        PlusModelEnum plusModelEnum = PlusModelEnum.getPlusModelEnum(modelId);
        switch (plusModelEnum) {
            case SRGH:
                try {
                    Integer userId = card.getUserId();
                    String orderSn = card.getOrderSn();
                    AuthIdentityDetail identity = authRepository.getUserIdentityDetail(userId,
                            card.getChannelId());
                    if (identity == null || StringUtils.isBlank(identity.getBirthday())) {
                        log.info("生成生日礼发放计划时间获取认证身份证信息为空：{},{}", userId, orderSn);
                        return null;
                    }
                    String birthday = identity.getBirthday().substring(5, 10);
                    // 生日礼只有开始时间，没有结束时间
                    // 如果用户已经有状态为已发放的发放计划(表示当年生日当天已发放过)，则当前年份顺延到下一年
                    List<MemberPlusSendPlanEntity> exist = memberPlusSendPlanRepository.listByUserAndModelId(
                            card.getUserId(), modelId, card.getConfigId(),
                            PlusSendPlanStatusEnum.DONE.getValue());
                    // 顺延到下一年生日
                    if (!CollectionUtils.isEmpty(exist)) {
                        Date date = org.apache.commons.lang3.time.DateUtils.addYears(new Date(), 1);
                        entity.setStartTime(DateUtils.getDateFromStr(
                                DateUtils.getDateTime(date).substring(0, 4) + "-" + birthday
                                        + " 00:00:00"));
                    } else {
                        // 今年生日
                        entity.setStartTime(DateUtils.getDateFromStr(
                                DateUtils.getSysCurFmtDate().substring(0, 4) + "-" + birthday
                                        + " 00:00:00"));
                    }
                    return entity;
                } catch (Exception e) {
                    LogUtil.printLog(e, "生成生日礼发放计划时间获取认证身份证信息异常");
                    return null;
                }
            default:
                entity.setStartTime(card.getStartTime());
                entity.setEndTime(card.getEndTime());
                break;
        }
        return entity;
    }

    @Override
    public void generateMemberProfitsSendPlanTask(List<MemberPlusSendPlanEntity> list) {
        List<MemberPlusSendPlanTaskEntity> taskEntityList = converter.toMemberPlusSendPlanTaskEntityList(
                list);
        if (CollectionUtils.isEmpty(taskEntityList)) {
            return;
        }
        taskEntityList.forEach(
                item -> item.setTaskState(PlusSendPlanTaskStatusEnum.WAIT_GRANT.getValue()));
        memberPlusSendPlanTaskRepository.batchInsertTask(taskEntityList);
    }

}
