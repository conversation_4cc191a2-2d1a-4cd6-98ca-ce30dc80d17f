package com.juzifenqi.plus.module.order.application.impl.strategy;

import com.alibaba.fastjson.JSON;
import com.juzifenqi.plus.constants.CommonConstant;
import com.juzifenqi.plus.enums.DeductOptSateEnum;
import com.juzifenqi.plus.enums.PlusPayTypeEnum;
import com.juzifenqi.plus.enums.PlusSmsSendNodeEnum;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.event.HandleCommonDeductEvent;
import com.juzifenqi.plus.module.order.model.event.HandleTaskDeductFailEvent;
import com.juzifenqi.plus.module.order.model.event.PlusDeductExcEvent;
import com.juzifenqi.plus.module.order.model.event.PlusDeductFailEvent;
import com.juzifenqi.plus.module.order.model.event.PlusDeductSuccessEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 会员月卡策略
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/08/15 17:00
 */
@Slf4j
@Component
public class YkApplicationHandler extends ApplicationStrategyHandler {

    @Override
    public void afterDeductSucDeal(PlusDeductSuccessEvent successEvent) {
        log.info("会员月卡划扣申请成功处理开始：{}", JSON.toJSONString(successEvent));
        Integer userId = successEvent.getUserId();
        String plusOrderSn = successEvent.getPlusOrderSn();
        String orderSn = successEvent.getOrderSn();
        PlusPayTypeEnum deductFlag = successEvent.getDeductFlag();
        // 公共后置处理
        HandleCommonDeductEvent paramVo = converter.toHandleCommonDeductEvent(successEvent);
        afterCommonHandle(paramVo, deductFlag);
        switch (deductFlag) {
            case PAY_TYPE_1:
                log.info("会员月卡立即划扣申请成功处理开始：{},{}", plusOrderSn, orderSn);
                // 发送借款单放款成功，划扣短信
                commonHandler.deductAmountSms(successEvent.getPlusOrderEntity(),
                        PlusSmsSendNodeEnum.NOD_7);
                break;
            case PAY_TYPE_7:
                log.info("会员月卡延迟划扣申请成功处理开始：{},{}", plusOrderSn, orderSn);
                // 修改划扣表处理状态=处理成功
                plusOrderDeductPlanModel.changeDeductPlanState(plusOrderSn,
                        DeductOptSateEnum.SUCCESS.getCode());
                // 发划扣成功短信
                commonHandler.deductAmountSms(successEvent.getPlusOrderEntity(),
                        PlusSmsSendNodeEnum.NOD_12);
                break;
            default:
                log.info("会员月卡非立即和延迟划扣申请成功不处理：{},{}", plusOrderSn, orderSn);
                break;
        }
        log.info("会员月卡划扣申请成功处理完成：{},{},{}", userId, plusOrderSn, orderSn);
    }

    @Override
    public void afterDeductFailDeal(PlusDeductFailEvent failEvent) {
        log.info("会员月卡划扣申请失败处理开始：{}", JSON.toJSONString(failEvent));
        PlusOrderEntity order = failEvent.getPlusOrderEntity();
        Integer userId = order.getUserId();
        String plusOrderSn = order.getOrderSn();
        String orderSn = failEvent.getOrderSn();
        PlusPayTypeEnum deductFlag = failEvent.getDeductFlag();
        // 公共后置处理
        HandleCommonDeductEvent paramVo = converter.toHandleCommonDeductEvent(failEvent);
        afterCommonHandle(paramVo, deductFlag);
        switch (deductFlag) {
            case PAY_TYPE_1:
                log.info("会员月卡立即划扣申请失败处理开始：{},{}", plusOrderSn, orderSn);
                // 非余额不足加入延迟划扣
                saveDelayDeductPlan(failEvent, order, orderSn, "会员月卡");
                // 需要判断如果是非绑卡原因失败的，都需要才加入后付款黑名单
                boolean saveBlack = !(failEvent.getResEntity() != null
                        && failEvent.getResEntity().getDeductRespEvent() != null
                        && CommonConstant.errorCardCode.equals(
                        failEvent.getResEntity().getDeductRespEvent().getCode()));
                if (saveBlack) {
                    // 加入后付款黑名单跑批任务
                    saveBlackListTask(order);
                }
                // 发送借款单放款成功，划扣失败短信
                commonHandler.deductAmountSms(order, PlusSmsSendNodeEnum.NOD_13);
                break;
            case PAY_TYPE_7:
                log.info("会员月卡延迟划扣申请失败处理开始：{},{}", plusOrderSn, orderSn);
                // 重置划扣任务状态（如果本次划扣返回的还是划扣失败且非余额不足，还要继续划，直到上限）
                HandleTaskDeductFailEvent deductFailEvent = converter.toHandlerTaskDeductFailEvent(
                        failEvent);
                plusOrderDeductPlanModel.resetDeduct(deductFailEvent);
                // 发送延迟划扣失败短信
                if (failEvent.getDeductNum() >= 5) {
                    commonHandler.deductAmountSms(order, PlusSmsSendNodeEnum.NOD_16);
                }
                break;
            default:
                log.info("会员月卡非立即和延迟类型划扣申请失败不处理：{},{}", plusOrderSn, orderSn);
                break;
        }
        log.info("会员月卡划扣申请失败处理完成：{},{},{}", userId, plusOrderSn, orderSn);
    }

    @Override
    public void afterDeductExcDeal(PlusDeductExcEvent param) {
        log.info("会员月卡划扣申请异常处理开始：{}", JSON.toJSONString(param));
        PlusOrderEntity order = param.getPlusOrderEntity();
        Integer userId = order.getUserId();
        String plusOrderSn = order.getOrderSn();
        String orderSn = param.getOrderSn();
        switch (param.getDeductFlag()) {
            case PAY_TYPE_7:
                log.info("会员月卡延迟划扣申请异常处理开始{},{},{}", userId, plusOrderSn, orderSn);
                PlusDeductFailEvent event = converter.toPlusDeductFailEvent(param);
                this.afterDeductFailDeal(event);
                break;
            default:
                log.info("会员月卡非延迟类型划扣申请异常不处理：{},{},{}", userId, plusOrderSn, orderSn);
                break;
        }
        log.info("会员月卡划扣申请异常处理完成：{},{},{}", userId, plusOrderSn, orderSn);
    }

    @Override
    @Deprecated
    public void afterDeductSucDealOld(PlusDeductSuccessEvent successEvent) {
        log.info("会员月卡同步划扣成功处理开始：{}", JSON.toJSONString(successEvent));
        Integer userId = successEvent.getUserId();
        String plusOrderSn = successEvent.getPlusOrderSn();
        String orderSn = successEvent.getOrderSn();
        PlusPayTypeEnum deductFlag = successEvent.getDeductFlag();
        // 公共后置处理
        HandleCommonDeductEvent paramVo = converter.toHandleCommonDeductEvent(successEvent);
        afterCommonHandleOld(paramVo, deductFlag);
        switch (deductFlag) {
            case PAY_TYPE_1:
                log.info("会员月卡立即划扣成功处理开始：{},{}", plusOrderSn, orderSn);
                // 发送借款单放款成功，划扣短信
                commonHandler.deductAmountSms(successEvent.getPlusOrderEntity(),
                        PlusSmsSendNodeEnum.NOD_7);
                break;
            case PAY_TYPE_7:
                log.info("会员月卡延迟划扣成功处理开始：{},{}", plusOrderSn, orderSn);
                // 修改划扣表处理状态=处理成功
                plusOrderDeductPlanModel.changeDeductPlanState(plusOrderSn,
                        DeductOptSateEnum.SUCCESS.getCode());
                // 发划扣成功短信
                commonHandler.deductAmountSms(successEvent.getPlusOrderEntity(),
                        PlusSmsSendNodeEnum.NOD_12);
                break;
            default:
                log.info("会员月卡非立即和延迟划扣成功不处理：{},{}", plusOrderSn, orderSn);
                break;
        }
        log.info("会员月卡同步划扣成功处理完成：{},{},{}", userId, plusOrderSn, orderSn);
    }


    @Override
    @Deprecated
    public void afterDeductFailDealOld(PlusDeductFailEvent failEvent) {
        log.info("会员月卡同步划扣失败处理开始：{}", JSON.toJSONString(failEvent));
        PlusOrderEntity order = failEvent.getPlusOrderEntity();
        Integer userId = order.getUserId();
        String plusOrderSn = order.getOrderSn();
        String orderSn = failEvent.getOrderSn();
        PlusPayTypeEnum deductFlag = failEvent.getDeductFlag();
        // 公共后置处理
        HandleCommonDeductEvent paramVo = converter.toHandleCommonDeductEvent(failEvent);
        afterCommonHandleOld(paramVo, deductFlag);
        switch (deductFlag) {
            case PAY_TYPE_1:
                log.info("会员月卡立即划扣失败处理开始：{},{}", plusOrderSn, orderSn);
                // 非余额不足加入延迟划扣
                saveDelayDeductPlanOld(failEvent, order, orderSn, "会员月卡");
                // 加入后付款黑名单跑批任务
                saveBlackListTask(order);
                // 发送借款单放款成功，划扣失败短信
                commonHandler.deductAmountSms(order, PlusSmsSendNodeEnum.NOD_13);
                break;
            case PAY_TYPE_7:
                log.info("会员月卡延迟划扣失败处理开始：{},{}", plusOrderSn, orderSn);
                // 重置划扣任务状态（如果本次划扣返回的还是划扣失败且非余额不足，还要继续划，直到上限）
                HandleTaskDeductFailEvent deductFailEvent = converter.toHandlerTaskDeductFailEvent(
                        failEvent);
                plusOrderDeductPlanModel.resetDeductOld(deductFailEvent);
                // 发送延迟划扣失败短信
                if (failEvent.getDeductNum() >= 5) {
                    commonHandler.deductAmountSms(order, PlusSmsSendNodeEnum.NOD_16);
                }
                break;
            default:
                log.info("会员月卡非立即和延迟类型划扣失败不处理：{},{}", plusOrderSn, orderSn);
                break;
        }
        log.info("会员月卡同步划扣失败处理完成：{},{},{}", userId, plusOrderSn, orderSn);
    }

    @Override
    @Deprecated
    public void afterDeductExcDealOld(PlusDeductExcEvent param) {
        log.info("会员月卡划扣异常处理开始：{}", JSON.toJSONString(param));
        PlusOrderEntity order = param.getPlusOrderEntity();
        Integer userId = order.getUserId();
        String plusOrderSn = order.getOrderSn();
        String orderSn = param.getOrderSn();
        switch (param.getDeductFlag()) {
            case PAY_TYPE_7:
                log.info("会员月卡延迟划扣异常处理开始{},{},{}", userId, plusOrderSn, orderSn);
                PlusDeductFailEvent event = converter.toPlusDeductFailEvent(param);
                this.afterDeductFailDealOld(event);
                break;
            default:
                log.info("会员月卡非延迟类型划扣异常不处理：{},{},{}", userId, plusOrderSn, orderSn);
                break;
        }
        log.info("会员月卡划扣异常处理完成：{},{},{}", userId, plusOrderSn, orderSn);
    }
} 