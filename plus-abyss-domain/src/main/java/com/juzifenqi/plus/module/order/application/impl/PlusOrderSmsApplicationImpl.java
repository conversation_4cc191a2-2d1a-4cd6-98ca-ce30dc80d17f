package com.juzifenqi.plus.module.order.application.impl;

import com.alibaba.fastjson.JSON;
import com.groot.utils.exception.LogUtil;
import com.juzifenqi.enumeration.OrderChannelEnum;
import com.juzifenqi.plus.config.ConfigProperties;
import com.juzifenqi.plus.constants.CommonConstant;
import com.juzifenqi.plus.enums.ChannelEnum;
import com.juzifenqi.plus.enums.JuziPlusEnum;
import com.juzifenqi.plus.enums.PlusSmsSendNodeEnum;
import com.juzifenqi.plus.module.asserts.model.MemberPlusQueryModel;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusInfoDetailEntity;
import com.juzifenqi.plus.module.common.IAiExternalRepository;
import com.juzifenqi.plus.module.common.ISmsRepository;
import com.juzifenqi.plus.module.common.event.PlusSmsParamEvent;
import com.juzifenqi.plus.module.order.application.IPlusOrderSmsApplication;
import com.juzifenqi.plus.module.order.model.PlusOrderDeductPlanModel;
import com.juzifenqi.plus.module.order.model.PlusOrderQueryModel;
import com.juzifenqi.plus.module.order.model.contract.IPlusAfterOrderRemindRepository;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderRepository;
import com.juzifenqi.plus.module.order.model.contract.IPlusResubmitCustomerGroupRepository;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusAfterOrderRemindEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderDeductPlanEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderSendSmsEvent;
import com.juzifenqi.plus.module.order.model.impl.strategy.HandlerContext;
import com.juzifenqi.plus.module.order.repository.po.PlusCustomerGroupRecordPo;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 订单发送短信
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/20 14:03
 */
@Slf4j
@Service
public class PlusOrderSmsApplicationImpl implements IPlusOrderSmsApplication {

    @Autowired
    private ConfigProperties                     configProperties;
    @Autowired
    private ISmsRepository                       smsRepository;
    @Autowired
    private IPlusOrderRepository                 orderRepository;
    @Autowired
    private PlusOrderQueryModel                  orderQueryModel;
    @Autowired
    private MemberPlusQueryModel                 plusQueryModel;
    @Autowired
    private IPlusResubmitCustomerGroupRepository groupRepository;
    @Autowired
    private PlusOrderDeductPlanModel             deductPlanModel;
    @Autowired
    private IAiExternalRepository                aiExternalRepository;
    @Autowired
    private IPlusAfterOrderRemindRepository      remindRepository;
    @Autowired
    private HandlerContext                       handlerContext;

    @Override
    public void sendWaitAfterPaySms(PlusOrderSendSmsEvent event) {
        try {
            log.info("借款单放款成功发送提醒短信开始：{}", JSON.toJSONString(event));
            if (!OrderChannelEnum.现金贷.getChannelCode()
                    .equals(event.getOrderChannelId())) {
                log.info("非商城渠道现金贷，不处理：{}", event.getOrderSn());
                return;
            }
            Integer userId = event.getUserId();
            String orderSn = event.getOrderSn();
            List<PlusOrderEntity> afterPayOrderInfoList = orderRepository.getAfterPayOrderInfoList(
                    userId, Arrays.asList(JuziPlusEnum.EXPEDITE_CARD.getCode(),
                            JuziPlusEnum.NEW_JUXP_CARD.getCode(), JuziPlusEnum.QUOTA_CARD.getCode(),
                            JuziPlusEnum.RDZX_CARD.getCode(), JuziPlusEnum.YITONG_CARD.getCode(),
                            JuziPlusEnum.HYYK_CARD.getCode()));
            if (CollectionUtils.isEmpty(afterPayOrderInfoList)) {
                log.info("借款单放款成功发送提醒短信-未获取到待支付的后付款订单，不处理：{},{}",
                        userId, orderSn);
                return;
            }
            PlusOrderEntity order = afterPayOrderInfoList.get(0);
            //发送短信
            PlusSmsParamEvent plusSmsParamVo = new PlusSmsParamEvent();
            plusSmsParamVo.setUserId(userId);
            plusSmsParamVo.setConfigId(order.getConfigId());
            plusSmsParamVo.setChannelId(order.getChannelId());
            plusSmsParamVo.setLoanOrderSn(orderSn);
            plusSmsParamVo.setSendNode(PlusSmsSendNodeEnum.NOD_9.getCode());
            plusSmsParamVo.setOrderSn(order.getOrderSn());
            smsRepository.sendSmsByConfig(plusSmsParamVo);
        } catch (Exception e) {
            LogUtil.printLog(e, "借款单放款成功发送提醒短信异常");
        }
    }

    @Override
    public void sendResubmitCardFailSms(PlusOrderSendSmsEvent event) {
        try {
            log.info("借款单放款失败发送重提卡放款失败提醒短信：{}", JSON.toJSONString(event));
            if (configProperties.resubmitSwitch.equals(CommonConstant.ZERO)) {
                log.info("借款单放款失败发送重提卡放款失败提醒短信,开关关闭：{}",
                        event.getOrderSn());
                return;
            }
            String orderSn = event.getOrderSn();
            boolean checkResult = checkPlusInfo(event, JuziPlusEnum.RESUBMIT_CARD.getCode());
            if (!checkResult) {
                log.info("借款单放款失败发送重提卡放款失败提醒短信,校验不通过：{}", orderSn);
                return;
            }
            // 借款金额<=3000才发短信
            if (event.getOrderAmount().compareTo(CommonConstant.THREE_THOUSAND) < 1) {
                PlusSmsParamEvent plusSmsParamVo = new PlusSmsParamEvent();
                plusSmsParamVo.setUserId(event.getUserId());
                plusSmsParamVo.setChannelId(event.getChannelId());
                plusSmsParamVo.setConfigId(JuziPlusEnum.RESUBMIT_CARD.getCode());
                plusSmsParamVo.setLoanOrderSn(event.getOrderSn());
                plusSmsParamVo.setSendNode(PlusSmsSendNodeEnum.NOD_8.getCode());
                smsRepository.sendSmsByConfig(plusSmsParamVo);
            }
        } catch (Exception e) {
            LogUtil.printLog(e, "借款单放款失败发送重提卡放款失败提醒短信异常");
        }
    }

    @Override
    public void sendExpCardMarketSms(PlusOrderSendSmsEvent event) {
        try {
            log.info("借款单进件资匹发送加速卡营销短信开始：{}", JSON.toJSONString(event));
            if (configProperties.expediteSwitch.equals(CommonConstant.ZERO)) {
                log.info("借款单进件资匹发送加速卡营销短信,开关关闭：{}", event.getOrderSn());
                return;
            }
            String orderSn = event.getOrderSn();
            boolean checkResult = checkPlusInfo(event, JuziPlusEnum.EXPEDITE_CARD.getCode());
            if (!checkResult) {
                log.info("借款单进件资匹发送加速卡营销短信,校验不通过：{}", orderSn);
                return;
            }
            PlusCustomerGroupRecordPo recordPo = groupRepository.getByOrderSn(orderSn);
            log.info("借款单进件资匹发送加速卡营销短信获取重提客群：{}",
                    JSON.toJSONString(recordPo));
            // 非重提客群单子才发短信
            if (recordPo == null) {
                PlusSmsParamEvent plusSmsParamVo = new PlusSmsParamEvent();
                plusSmsParamVo.setUserId(event.getUserId());
                plusSmsParamVo.setConfigId(JuziPlusEnum.EXPEDITE_CARD.getCode());
                plusSmsParamVo.setChannelId(event.getChannelId());
                plusSmsParamVo.setSendNode(PlusSmsSendNodeEnum.NOD_10.getCode());
                plusSmsParamVo.setLoanOrderSn(orderSn);
                MemberPlusInfoDetailEntity memberPlusInfo = plusQueryModel.getUserLastInfo(
                        event.getUserId(), event.getChannelId(),
                        JuziPlusEnum.EXPEDITE_CARD.getCode());
                if (memberPlusInfo != null) {
                    plusSmsParamVo.setOrderSn(memberPlusInfo.getOrderSn());
                    plusSmsParamVo.setProgramId(memberPlusInfo.getProgramId());
                }
                smsRepository.sendSmsByConfig(plusSmsParamVo);
            }
        } catch (Exception e) {
            LogUtil.printLog(e, "借款单进件资匹发送加速卡营销短信异常");
        }
    }

    @Override
    public void sendCustomerGroupJeSms(PlusOrderSendSmsEvent event) {
        try {
            log.info("发送重提客群借款单降额短信开始：{}", JSON.toJSONString(event));
            if (!OrderChannelEnum.现金贷.getChannelCode().equals(event.getOrderChannelId())) {
                log.info("发送降额短信，非现金贷渠道，用户id：{}", event.getUserId());
                return;
            }
            String orderSn = event.getOrderSn();
            Integer userId = event.getUserId();
            // 是否重提客群
            PlusCustomerGroupRecordPo record = groupRepository.getByOrderSn(orderSn);
            if (record == null) {
                log.info("发送降额短信，非重提客群用户，订单号：{}，用户id：{}", orderSn, userId);
                return;
            }
            if (StringUtils.isBlank(record.getRejectOrderSn())) {
                log.info("发送降额短信，被拒订单号为空，订单号：{}，用户id：{}", orderSn, userId);
                return;
            }
            //发送短信，短信参数走策略
            PlusSmsParamEvent plusSmsParamVo = new PlusSmsParamEvent();
            plusSmsParamVo.setUserId(userId);
            plusSmsParamVo.setConfigId(JuziPlusEnum.EXPEDITE_CARD.getCode());
            plusSmsParamVo.setChannelId(event.getChannelId());
            plusSmsParamVo.setSendNode(PlusSmsSendNodeEnum.NOD_14.getCode());
            plusSmsParamVo.setLoanOrderSn(record.getRejectOrderSn());
            MemberPlusInfoDetailEntity memberPlusInfo = plusQueryModel.getUserLastInfo(
                    JuziPlusEnum.EXPEDITE_CARD.getCode(), userId, event.getChannelId());
            if (memberPlusInfo != null) {
                plusSmsParamVo.setOrderSn(memberPlusInfo.getOrderSn());
                plusSmsParamVo.setProgramId(memberPlusInfo.getProgramId());
            }
            smsRepository.sendSmsByConfig(plusSmsParamVo);
        } catch (Exception e) {
            LogUtil.printLog(e, "发送重提客群借款单降额短信异常");
        }
    }

    @Override
    public void delayDeductSms() {
        log.info("批量发送延迟划扣提醒短信开始");
        List<PlusOrderDeductPlanEntity> delayDeductSmsList = deductPlanModel.getDelayDeductSmsList();
        if (CollectionUtils.isEmpty(delayDeductSmsList)) {
            log.info("批量发送延迟划扣提醒短信列表为空");
            return;
        }
        for (PlusOrderDeductPlanEntity entity : delayDeductSmsList) {
            PlusSmsParamEvent plusSmsParamVo = new PlusSmsParamEvent();
            plusSmsParamVo.setLoanOrderSn(entity.getOrderSn());
            plusSmsParamVo.setUserId(entity.getUserId());
            plusSmsParamVo.setConfigId(entity.getConfigId());
            plusSmsParamVo.setChannelId(entity.getChannelId());
            plusSmsParamVo.setSendNode(PlusSmsSendNodeEnum.NOD_6.getCode());
            plusSmsParamVo.setOrderSn(entity.getPlusOrderSn());
            plusSmsParamVo.setLoanOrderSn(entity.getOrderSn());
            smsRepository.sendSmsByConfig(plusSmsParamVo);
            // 修改任务状态=完成
            deductPlanModel.deductSmsDone(entity.getId());
            // 94ai机器人外呼电话
            aiExternalRepository.sendOutBoundVirtual(entity.getUserId(), entity.getConfigId());
        }
        log.info("批量发送延迟划扣提醒短信结束");
    }

    @Override
    public void waitPaySendSms() {
        //1 查询待处理的短信
        List<PlusAfterOrderRemindEntity> list = remindRepository.getRemindList();
        if (CollectionUtils.isEmpty(list)) {
            log.info("需要提醒支付的订单列表为空");
            return;
        }
        log.info("批量后付款支付提醒短信数据开始:{}", list.size());
        for (PlusAfterOrderRemindEntity remind : list) {
            try {
                handlerContext.afterOrderSendMsgAndPush(remind);
            } catch (Exception e) {
                log.info("批量处理付款支付提醒短信数据异常:{}", remind.getOrderSn());
            }
        }
    }

    @Override
    public void xeykRenewRemind() {
        //1 查询待处理的短信
        List<PlusAfterOrderRemindEntity> list = remindRepository.getRemindListByConfigId(
                JuziPlusEnum.XEYK_CARD.getCode());
        if (CollectionUtils.isEmpty(list)) {
            log.info("小额月卡需要提醒支付的订单列表为空");
            return;
        }
        log.info("小额月卡批量后付款支付提醒短信数据开始:{}", list.size());
        for (PlusAfterOrderRemindEntity remind : list) {
            try {
                handlerContext.afterOrderSendMsgAndPush((remind));
            } catch (Exception e) {
                log.info("小额月卡批量处理付款支付提醒短信数据异常:{}", remind.getOrderSn());
            }
        }
    }

    @Override
    public void initAiOutboundMobileJob(Integer size) {
        aiExternalRepository.initAiOutboundMobileJob(size);
    }

    /**
     * * 重提卡与加速卡节点发送短信前校验
     */
    public boolean checkPlusInfo(PlusOrderSendSmsEvent event, Integer configId) {
//        if (!Objects.equals(event.getChannelId(), ChannelEnum.A.getCode())) {
//            log.info("重提卡与加速卡节点发送短信前校验,非商城渠道：{}", event.getOrderSn());
//            return false;
//        }
        Integer userId = event.getUserId();
        Integer channelId = event.getChannelId();
        // 判断用户七天内是否购买过会员
        List<PlusOrderEntity> sevenPlusOrder = orderQueryModel.getSevenPlusOrder(userId, channelId);
        if (!CollectionUtils.isEmpty(sevenPlusOrder)) {
            log.info("重提卡与加速卡节点发送短信前校验,7天内买过卡：{}", event.getOrderSn());
            return false;
        }
        // 判断当前用户是否是会员
        MemberPlusInfoDetailEntity detail = plusQueryModel.getUserLastInfo(userId, channelId,
                configId);
        if (detail != null) {
            log.info("重提卡与加速卡节点发送短信前校验,已是会员身份：{}", event.getOrderSn());
            return false;
        }
        return true;
    }
}
