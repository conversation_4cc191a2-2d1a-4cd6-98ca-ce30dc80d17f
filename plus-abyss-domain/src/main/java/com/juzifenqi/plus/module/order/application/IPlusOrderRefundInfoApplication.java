package com.juzifenqi.plus.module.order.application;

import com.juzifenqi.plus.dto.resp.PlusAbyssResult;
import com.juzifenqi.plus.module.order.model.contract.entity.callback.OrderRefundNotifyEntity;
import com.juzifenqi.plus.module.order.model.event.order.OrderRefundSecondEvent;

/**
 * <AUTHOR>
 * @date 2024/9/7 14:27
 */
public interface IPlusOrderRefundInfoApplication {

    /**
     * 原路退款、原路换卡退款
     */
    void originalRefund(OrderRefundNotifyEntity entity);


    /**
     * 会员退费监控
     * @param lastId 上次处理的退款记录id
     * @return true
     */
    Boolean refundPlusOrderMonitor(Integer lastId);

    /**
     * 第二次退款
     */
    void secondRefund(OrderRefundSecondEvent event);

}
