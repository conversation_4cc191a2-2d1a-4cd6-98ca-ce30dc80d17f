package com.juzifenqi.plus.module.order.application.impl;

import com.alibaba.fastjson.JSON;
import com.groot.utils.exception.LogUtil;
import com.juzifenqi.enumeration.OrderStateEnum;
import com.juzifenqi.plus.config.ConfigProperties;
import com.juzifenqi.plus.constants.CommonConstant;
import com.juzifenqi.plus.enums.*;
import com.juzifenqi.plus.enums.pay.PayProductCodeEnum;
import com.juzifenqi.plus.exception.PlusAbyssException;
import com.juzifenqi.plus.module.asserts.application.IMemberPlusSendPlanApplication;
import com.juzifenqi.plus.module.asserts.model.MemberPlusQueryModel;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusInfoDetailEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusInfoEntity;
import com.juzifenqi.plus.module.asserts.model.event.LoanSucProfitsEvent;
import com.juzifenqi.plus.module.common.IMemberSwitchControlRepository;
import com.juzifenqi.plus.module.common.entity.MemberPlusSwitchControlEntity;
import com.juzifenqi.plus.module.order.application.IPlusOrderApplication;
import com.juzifenqi.plus.module.order.application.IPlusRefundRecordApplication;
import com.juzifenqi.plus.module.order.application.converter.IPlusRefundRecordApplicationConverter;
import com.juzifenqi.plus.module.order.application.validator.PlusRefundRecordValidator;
import com.juzifenqi.plus.module.order.model.IPlusRefundRecordModel;
import com.juzifenqi.plus.module.order.model.PlusOrderDetailModel;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderSeparateRepository;
import com.juzifenqi.plus.module.order.model.contract.entity.separate.PlusOrderSeparateEntity;
import com.juzifenqi.plus.module.order.model.event.CreateRefundMqRecordEvent;
import com.juzifenqi.plus.module.order.model.event.order.CreateRefundRecordEvent;
import com.juzifenqi.plus.module.order.model.event.order.LoanOrderCloseEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderCancelEvent;
import com.juzifenqi.plus.module.order.model.event.order.RefundExecuteEvent;
import com.juzifenqi.plus.module.order.model.impl.strategy.HandlerContext;
import com.juzifenqi.plus.module.order.repository.po.PlusMqOrderStatePo;
import com.juzifenqi.plus.module.order.repository.po.PlusRefundRecordPo;

import java.util.*;
import java.util.stream.Collectors;

import com.juzishuke.framework.common.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 急速/延迟退款
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/26 10:28
 */
@Slf4j
@Service
public class PlusRefundRecordApplicationImpl implements IPlusRefundRecordApplication {

    private final IPlusRefundRecordApplicationConverter converter = IPlusRefundRecordApplicationConverter.instance;

    @Autowired
    private ConfigProperties configProperties;
    @Autowired
    private IPlusRefundRecordModel refundRecordModel;
    @Autowired
    private PlusRefundRecordValidator validator;
    @Autowired
    private MemberPlusQueryModel plusQueryModel;
    @Autowired
    private IPlusOrderApplication orderApplication;
    @Autowired
    private IMemberPlusSendPlanApplication sendPlanApplication;
    @Autowired
    private IMemberSwitchControlRepository controlRepository;
    @Autowired
    private IPlusOrderSeparateRepository plusOrderSeparateRepository;
    @Autowired
    private HandlerContext handlerContext;

    @Override
    public void createRefundRecord(CreateRefundRecordEvent event) {
        // 入参校验
        validator.checkParam(event);
        if (event.getCancelType() == PlusCancelTypeEnum.JS.getValue()) {
            refundRecordModel.createRapidPlusRefundRecord(event);
            return;
        }
        refundRecordModel.createDelayPlusRefundRecord(event);
    }

    @Override
    public void createRefundRecordNoCheck(CreateRefundRecordEvent event) {
        refundRecordModel.createRapidPlusRefundRecordNoCheck(event);
    }

    @Override
    public void delayRefundExecute(Integer size) {
        refundRecordModel.delayRefundExecute(size);
    }

    @Override
    public void refundExecute(RefundExecuteEvent event) {
        log.info("急速退款任务执行开始：{}", JSON.toJSONString(event));
        List<PlusRefundRecordPo> jobList = refundRecordModel.getJobList(event);
        if (CollectionUtils.isEmpty(jobList)) {
            log.info("急速退款待执行任务列表为空");
            return;
        }
        // 批量修改为处理中
        refundRecordModel.batchUpdateState(
                jobList.stream().map(PlusRefundRecordPo::getId).collect(Collectors.toList()),
                PlusRefundStatusEnum.PlUS_REFUND_4, PlusCancelTypeEnum.JS.getValue());
        for (PlusRefundRecordPo record : jobList) {
            Integer userId = record.getUserId();
            String orderSn = record.getOrderSn();
            // 此会员单号没用，实际上退款还是实时查询
            String plusOrderSn = record.getPlusOrderSn();
            log.info("极速退款逻辑处理开始,订单号:{},会员单号:{},用户id:{}", orderSn, plusOrderSn,
                    userId);
            try {
                //是否是会员
                MemberPlusInfoDetailEntity detail = plusQueryModel.getUserLastInfo(userId,
                        record.getChannelId(), record.getConfigId());
                log.info("急速退款任务-查询会员信息:{}", JSON.toJSONString(detail));
                if (detail == null) {
                    log.info("急速退款任务-用户非会员：{},{},{}", userId, plusOrderSn, orderSn);
                    handleRecord(record, event, PlusRefundStatusEnum.PlUS_REFUND_1,
                            PlusRefundLogTypeEnum.LOG_TYPE_20, "");
                    continue;
                }
                
                //判断是支付宝支付并且时间小于10分钟
                List<PlusOrderSeparateEntity> orderSeparateList = plusOrderSeparateRepository.getOrderSeparateByOrderNo(plusOrderSn);
                if (!CollectionUtils.isEmpty(orderSeparateList)) {
                    Optional<PlusOrderSeparateEntity> zfbSeparateRecord = orderSeparateList.stream().filter(
                            t -> Objects.equals(t.getSeparateState(), SeparateStateEnum.SUCCESS.getCode())
                                    && Objects.equals(t.getRemark(), PayProductCodeEnum.ZFBZF.getCode())).findFirst();
                    if (zfbSeparateRecord.isPresent() && zfbSeparateRecord.get().getPayCallbackTime() != null
                            && DateUtil.addMiunte(zfbSeparateRecord.get().getPayCallbackTime(), configProperties.zfbRefundDelay).after(new Date())) {
                        log.info("orderSn:{}, plusOrderSn:{} | 支付宝订单退款延迟进行退款", orderSn, plusOrderSn);
                        handleRecord(record, event, PlusRefundStatusEnum.PlUS_REFUND_5,
                                PlusRefundLogTypeEnum.LOG_TYPE_32, "支付宝延迟退款");
                        continue;
                    }
                }
                
                //判断会员单是否处于支付中状态，并且当前时间减掉支付记录创建时间>间隔时间才允许退款
                if (!CollectionUtils.isEmpty(orderSeparateList)) {
                    Optional<PlusOrderSeparateEntity> payingRecord = orderSeparateList.stream()
                            .filter(t -> Objects.equals(t.getSeparateState(), SeparateStateEnum.PROCESSING.getCode()))
                            .max(Comparator.comparing(PlusOrderSeparateEntity::getCreateTime));
                    
                    if (payingRecord.isPresent()) {
                        PlusOrderSeparateEntity payRecord = payingRecord.get();
                        Date createTime = payRecord.getCreateTime();
                        if (createTime != null) {
                            // 获取配置的间隔时间（毫秒）
                            long intervalMillis = configProperties.payInterval * 60 * 1000;
                            // 获取当前时间
                            Date now = new Date();
                            // 计算时间差（毫秒）
                            long diffMillis = now.getTime() - createTime.getTime();
                            // 如果未超过配置的间隔时间，则不执行退款
                            if (diffMillis <= intervalMillis) {
                                log.info("orderSn:{}, plusOrderSn:{} | 支付中状态的订单退款延迟进行退款，当前间隔时间:{}毫秒，配置间隔时间:{}毫秒", 
                                        orderSn, plusOrderSn, diffMillis, intervalMillis);
                                handleRecord(record, event, PlusRefundStatusEnum.PlUS_REFUND_0,
                                        PlusRefundLogTypeEnum.LOG_TYPE_32, "支付中状态订单延迟退款");
                                continue;
                            }
                        }
                    }
                }
                
                try {
                    // 取消订单，注：取消订单号为detail.getOrderSn()，非record.getPlusOrderSn()
                    PlusOrderCancelEvent cancelEvent = converter.toPlusOrderCancelEvent(record,
                            PlusCancelTypeEnum.JS.getValue(),
                            CancelReasonEnum.CANCEL_REASON_NEW_REFUND.getCode(),
                            detail.getOrderSn(), 0, "极速退款取消");
                    orderApplication.cancelOrderApply(cancelEvent);
                    handleRecord(record, event, PlusRefundStatusEnum.PlUS_REFUND_1,
                            PlusRefundLogTypeEnum.LOG_TYPE_23, "处理成功");
                    log.info("极速退款逻辑处理成功：{},{}", orderSn, plusOrderSn);
                } catch (Exception e) {
                    LogUtil.printLog(e, "取消会员订单异常");
                    // 业务异常不需要重新跑批，系统异常需要重新跑批
                    handleRecord(record, event,
                            e instanceof PlusAbyssException ? PlusRefundStatusEnum.PlUS_REFUND_1
                                    : PlusRefundStatusEnum.PlUS_REFUND_2,
                            PlusRefundLogTypeEnum.LOG_TYPE_22,
                            e instanceof PlusAbyssException ? e.getMessage() : "取消订单异常");
                }
            } catch (Exception e) {
                log.info("急速退款任务出现未知异常：{},{},{}", userId, orderSn, plusOrderSn);
                LogUtil.printLog("急速退款任务处理异常", e);
                handleRecord(record, event, PlusRefundStatusEnum.PlUS_REFUND_2,
                        PlusRefundLogTypeEnum.LOG_TYPE_24, "");
            }
        }
        log.info("急速退款任务执行结束");
    }

    @Override
    public void createRefundMqRecord(CreateRefundMqRecordEvent event) {
        refundRecordModel.createRefundMqRecord(event);
    }

    @Override
    public void createRefundMqRecordV2(CreateRefundMqRecordEvent event) {
        refundRecordModel.createRefundMqRecordV2(event);
    }

    /**
     * 获取借款单状态枚举
     */
    private OrderStateEnum getOrderStateEnum(Integer orderState) {
        for (OrderStateEnum value : OrderStateEnum.values()) {
            if (Objects.equals(value.getState(), orderState)) {
                return value;
            }
        }
        return null;
    }

    @Override
    public void mqRecordExecute() {
        log.info("订单中心状态变更任务处理开始");
        List<PlusMqOrderStatePo> jobList = refundRecordModel.getMqRecordJobList();
        if (CollectionUtils.isEmpty(jobList)) {
            log.info("订单中心状态变更任务处理列表为空");
            return;
        }
        // 批量修改为处理中
        refundRecordModel.batchUpdateMqState(jobList, PlusRefundStatusEnum.PlUS_REFUND_4);
        for (PlusMqOrderStatePo po : jobList) {
            try {
                OrderStateEnum orderStateEnum = getOrderStateEnum(po.getOrderStatus());
                if (orderStateEnum == null) {
                    refundRecordModel.saveRefundLog(po, PlusRefundLogTypeEnum.LOG_TYPE_40);
                    return;
                }
                switch (orderStateEnum) {
                    case 分期还款中:
                        LoanSucProfitsEvent event = converter.toLoanSucProfitsEvent(po);
                        sendPlanApplication.loanSucProfitEvent(event);
                        updateMqStateInfo(po, PlusRefundStatusEnum.PlUS_REFUND_1);
                        break;
                    case 交易复核未通过:
                    case 交易关闭:
                    case 放款失败:
                    case 交易关闭赋强公证超时:
                        LoanOrderCloseEvent closeEvent = converter.toLoanOrderCloseEvent(po);
                        loanOrderClose(closeEvent, po, orderStateEnum);
                        break;
                    default:
                        log.info("订单状态变更任务处理,未知订单状态：{},{}", po.getOrderSn(),
                                po.getUserId());
                        break;
                }
            } catch (Exception e) {
                updateMqStateInfo(po, PlusRefundStatusEnum.PlUS_REFUND_2);
                LogUtil.printLog(e, "处理订单状态变更任务异常");
            }
        }
        log.info("订单中心状态变更任务处理结束");
    }

    /**
     * 借款单关闭处理
     * <p>以前是3个状态分开处理的，但是处理的前置逻辑完全一样，所以这里合并了</p>
     */
    private void loanOrderClose(LoanOrderCloseEvent event, PlusMqOrderStatePo po,
                                OrderStateEnum state) {
        log.info("借款单关闭处理开始：{}，{}", state.getStateDesc(), JSON.toJSONString(event));
        Integer userId = event.getUserId();
        String orderSn = event.getLoanOrderSn();
        try {
            // 极速退款总开关是否开启，目前是商城渠道才校验
//            if (Objects.equals(event.getChannelId(), ChannelEnum.A.getCode())) {
                MemberPlusSwitchControlEntity plusSwitchByType = controlRepository.getSwitchByCode(
                        PlusSwitchEnum.JSTK_PlUS_TOTAL.getCode());
                if (CommonConstant.ONE != plusSwitchByType.getStatus()) {
                    log.info("借款单关闭处理处理退款总开关未开启:{},{}", userId, orderSn);
                    updateMqStateInfo(po, PlusRefundStatusEnum.PlUS_REFUND_3);
                    refundRecordModel.saveRefundLog(po, PlusRefundLogTypeEnum.LOG_TYPE_0);
                    return;
                }
//            }
            List<MemberPlusInfoEntity> memberPlusInfoList = plusQueryModel.getMemberPlusInfoList(
                    userId, event.getChannelId());
            if (CollectionUtils.isEmpty(memberPlusInfoList)) {
                log.info("借款单关闭处理当前用户不是会员:{},{}", userId, orderSn);
                refundRecordModel.saveRefundLog(po, PlusRefundLogTypeEnum.LOG_TYPE_2);
                return;
            }
            for (MemberPlusInfoEntity entity : memberPlusInfoList) {
                event.setConfigId(entity.getConfigId());
                switch (state) {
                    case 交易复核未通过:
                        // 风控闭单
                        handlerContext.loanFkClose(event);
                        break;
                    case 交易关闭:
                    case 交易关闭赋强公证超时:
                        // 取消订单
                        handlerContext.loanCancel(event);
                        break;
                    case 放款失败:
                        // 资匹闭单
                        handlerContext.loanZpClose(event);
                        break;
                    default:
                        log.info("借款单关闭处理,未知订单状态：{},{}", po.getOrderSn(), userId);
                        break;
                }
            }
            updateMqStateInfo(po, PlusRefundStatusEnum.PlUS_REFUND_1);
            log.info("借款单关闭处理完成:{},{},{}", userId, orderSn, state.getStateDesc());
        } catch (Exception e) {
            refundRecordModel.saveRefundLog(po, PlusRefundLogTypeEnum.LOG_TYPE_13);
            LogUtil.printLog("借款单关闭处理异常：" + orderSn, e);
            throw e;
        }
    }

    /**
     * 修改mq信息
     */
    private void updateMqStateInfo(PlusMqOrderStatePo po, PlusRefundStatusEnum statusEnum) {
        po.setRemark(statusEnum.getName());
        po.setOptStatus(statusEnum.getCode());
        if (statusEnum == PlusRefundStatusEnum.PlUS_REFUND_2) {
            po.setNum((po.getNum() == null ? 0 : po.getNum()) + 1);
        }
        refundRecordModel.updateMqStateInfo(po);
    }

    /**
     * 处理急速退款任务信息
     */
    private void handleRecord(PlusRefundRecordPo record, RefundExecuteEvent event,
                              PlusRefundStatusEnum statusEnum, PlusRefundLogTypeEnum logTypeEnum, String remark) {
        record.setOptStatus(statusEnum.getCode());
        record.setRemark(statusEnum.getName());
        record.setNum(event.isRetry() ? record.getNum() + 1 : record.getNum());
        refundRecordModel.updateRefundRecord(record);
        refundRecordModel.saveRefundLog(record, logTypeEnum, remark);
    }
}
