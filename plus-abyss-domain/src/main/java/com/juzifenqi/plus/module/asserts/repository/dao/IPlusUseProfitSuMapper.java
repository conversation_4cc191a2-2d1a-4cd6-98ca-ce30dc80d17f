package com.juzifenqi.plus.module.asserts.repository.dao;

import com.juzifenqi.plus.module.asserts.repository.po.PlusUseProfitSuPo;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 加速卡专属权益使用记录
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/24 16:44
 */
@Mapper
public interface IPlusUseProfitSuMapper {

    /**
     * 新增返回ID
     */
    Integer savePlusUseProfitSu(PlusUseProfitSuPo plusUseProfitSu);

    /**
     * 删除
     */
    Integer deletePlusUseProfitSu(@Param("id") Integer id);

    /**
     * 更新
     */
    Integer updatePlusUseProfitSu(@Param("plusUseProfitSu") PlusUseProfitSuPo plusUseProfitSu);

    /**
     * Load查询
     */
    PlusUseProfitSuPo loadPlusUseProfitSu(@Param("id") Integer id);

    /**
     * 分页查询Data
     */
    List<PlusUseProfitSuPo> pageList(@Param("offset") Integer offset,
                                     @Param("pagesize") Integer pagesize);

    /**
     * 分页查询Count
     */
    Integer pageListCount(@Param("offset") Integer offset, @Param("pagesize") Integer pagesize);

    /**
     * Load查询
     */
    PlusUseProfitSuPo loadPlusOrder(@Param("plusOrderSn") String plusOrderSn, @Param("orderSn") String orderSign);

    /**
     * 根据用户id查询
     */
    List<PlusUseProfitSuPo> selectByUserId(@Param("userId") Integer userId);

    /**
     * 根据会员订单号查询记录
     */
    PlusUseProfitSuPo selectByPlusOrderSn(@Param("plusOrderSn") String plusOrderSn);


    List<PlusUseProfitSuPo> selectByPlusOrderSnCommon(@Param("plusOrderSn") String plusOrderSn);


}
