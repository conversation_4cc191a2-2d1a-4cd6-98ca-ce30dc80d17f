package com.juzifenqi.plus.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.bean.ConsumerBean;
import com.juzifenqi.mq.consumer.normal.NormalConsumerClient;
import com.juzifenqi.order.dto.OrderQueryDTO;
import com.juzifenqi.plus.config.ConfigProperties;
import com.juzifenqi.plus.config.PlusMqConfig;
import com.juzifenqi.plus.enums.JuziPlusEnum;
import com.juzifenqi.plus.enums.PlusCancelTypeEnum;
import com.juzifenqi.plus.module.asserts.model.MemberPlusQueryModel;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusInfoDetailEntity;
import com.juzifenqi.plus.module.common.repository.external.acl.IMRepositoryAcl;
import com.juzifenqi.plus.module.order.application.IPlusRefundRecordApplication;
import com.juzifenqi.plus.module.order.model.contract.entity.callback.AuthFailEntity;
import com.juzifenqi.plus.module.order.model.contract.external.IOrderExternalRepository;
import com.juzifenqi.plus.module.order.model.event.order.CreateRefundRecordEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/6 15:25
 * @description 认证失效，会员退款
 */
@Configuration
@Slf4j
public class AuthFailListener extends NormalConsumerClient {

    @Autowired
    private PlusMqConfig mqConfig;
    @Autowired
    private ConfigProperties configProperties;
    @Autowired
    private MemberPlusQueryModel memberPlusQueryModel;
    @Autowired
    private IOrderExternalRepository orderExternalRepository;
    @Autowired
    private IPlusRefundRecordApplication refundRecordApplication;
    @Autowired
    private IMRepositoryAcl imRepositoryAcl;


    @PostConstruct
    public ConsumerBean init() {
        return initConsumer(mqConfig.getAuthInvalidationGroupId(), mqConfig.getAuthInvalidationTopic());
    }

    @Override
    protected boolean dealMessage(Message message, ConsumeContext consumeContext) {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("认证失效消息start：{}", body);
        AuthFailEntity authFailEntity = JSON.parseObject(body, AuthFailEntity.class);
        if (authFailEntity == null) {
            log.info("认证失效消息为空：{}", body);
            return true;
        }

        Integer userId = authFailEntity.getUserId();
        Integer channel = authFailEntity.getChannel();
        Date authTime = authFailEntity.getSuccessTime();
        try {
            if (!configProperties.authChannel.contains(channel.toString())) {
                log.info("用户{}，渠道{}，认证时间{}，非桔小花渠道，无需退款", userId, channel, authTime);
                return true;
            }

            //获取认证成功后，开通的会员
            List<Integer> configIds = Arrays.asList(JuziPlusEnum.EXPEDITE_CARD.getCode(), JuziPlusEnum.NEW_JUXP_CARD.getCode(),
                    JuziPlusEnum.QUOTA_CARD.getCode(), JuziPlusEnum.YITONG_CARD.getCode(), JuziPlusEnum.HYYK_CARD.getCode());
            MemberPlusInfoDetailEntity plusInfoDetail = memberPlusQueryModel.getMemberPlusAfterAuthTime(userId, channel, configIds, authTime);
            if (plusInfoDetail == null) {
                log.info("用户{}，渠道{}，认证时间{}，未开通会员，无需退款", userId, channel, authTime);
                return true;
            }

            //获取会员开通后，有在途的订单,返回
            List<OrderQueryDTO> onWayOrderByUserId = orderExternalRepository.getOnWayOrderByUserId(userId);
            if (!CollectionUtils.isEmpty(onWayOrderByUserId)) {
                log.info("用户{}，渠道{}，认证时间{}，有在途订单，无需退款", userId, channel, authTime);
                return true;
            }

            //创建极速退款任务
            CreateRefundRecordEvent event = new CreateRefundRecordEvent();
            event.setUserId(userId);
            event.setConfigId(plusInfoDetail.getConfigId());
            event.setPlusOrderSn(plusInfoDetail.getOrderSn());
            event.setChannelId(channel);
            event.setCancelType(PlusCancelTypeEnum.JS.getValue());
            refundRecordApplication.createRefundRecordNoCheck(event);
            log.info("认证失效消息end：{}", body);
            return true;
        } catch (Exception e) {
            String msg = String.format("认证失效，退款会员失败，userId=%s,channel=%s,authTime=%s", userId, channel, authTime);
            log.error(msg, e);
            imRepositoryAcl.sendImMessage(msg);
            return false;
        }
    }
}
