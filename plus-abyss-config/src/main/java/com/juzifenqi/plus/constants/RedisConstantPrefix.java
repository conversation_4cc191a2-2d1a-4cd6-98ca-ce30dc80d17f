package com.juzifenqi.plus.constants;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/2/8 11:00 AM
 */
public class RedisConstantPrefix {

    public final static String BASE_PREFIX = "super_plus:";

    /**
     * 领取优惠券 -- 预防重提
     */
    public final static String MEMBER_COUPON_PLUS_KEY = BASE_PREFIX + "MEMBER_INFO_COUPONID_";

    /**
     * 开通会员 -- 预防重提
     */
    public final static String MEMBER_OPEN_PLUS_KEY = BASE_PREFIX + "MEMBER_INFO_OPEN_";

    /**
     * 待支付订单继续支付 -- 预防重提
     */
    public final static String MEMBER_OPEN_CONTINUE_PAY = BASE_PREFIX + "MEMBER_OPEN_CONTINUE_PAY_";

    /**
     * 半价商品全部缓存-包含转换后的数据
     */
    public final static String HALF_PRICE_PRODUCTS_KEY = BASE_PREFIX + "HALF_PRICE_PRODUCTS_KEY";

    /**
     * 全部商品id集合
     */
    public final static String HALF_PRICE_PRODUCT_KEY_ALL  =
            BASE_PREFIX + "HALF_PRICE_PRODUCT_KEY_ALL";
    /**
     * /** 前N个半价商品信息
     */
    public final static String HALF_PRICE_PRODUCTS_TOP_KEY =
            BASE_PREFIX + "HALF_PRICE_PRODUCTS_TOP_";

    /**
     * 方案落地页 方案的基础权益
     */
    public final static String PROGRAM_BASIC_PROFIT_KEY = BASE_PREFIX + "PROGRAM_BASIC_PROFIT_KEY_";

    /**
     * 方案ID
     */
    public final static String MEMBER_PROGRAM_ID_KEY = BASE_PREFIX + "MEMBER_PROGRAM_ID_KEY_";

    /**
     * 会员节订单是否显示生活权益
     */
    public final static String ACTIVITY_SHOW_PROFIT = BASE_PREFIX + "ACTIVITY_ORDER_SHOW_PROFIT_";

    /**
     * 会员购买记录
     */
    public final static String MEMBER_PLUS_BUY_RECORD = BASE_PREFIX + "MEMBER_PLUS_BUY_RECORD_";

    /**
     * 7天内是否购买过桔享会员
     */
    public final static String SEVEN_MEMBER_INFO_KEY = BASE_PREFIX + "SEVEN_MEMBER_INFO_";


    /**
     * 会员周期 -- 预防并发执行
     */
    public final static String MEMBER_PLUS_INFO_KEY = BASE_PREFIX + "MEMBER_PLUS_INFO";


    /**
     * 会员过期 -- 预防重复划扣
     */
    public final static String MEMBER_PLUS_RENEW_ORDER = BASE_PREFIX + "MEMBER_PLUS_RENEW_ORDER_";

    /**
     * 会员周期详情
     */
    public final static String PLUS_MEMBER_INFO_LIST = BASE_PREFIX + "PLUS_MEMBER_INFO_LIST_NEW";

    /**
     * 会员身份详情
     */
    public final static String PLUS_MEMBER_INFO_DETAIL_LIST =
            BASE_PREFIX + "PLUS_MEMBER_INFO_DETAIL_LIST_NEW";

    /**
     * 最新一条开通会员数据
     */
    public final static String PLUS_LAST_MEMBER_INFO_DETAIL =
            BASE_PREFIX + "PLUS_LAST_MEMBER_INFO_DETAIL_";

    /**
     * 全部类型会员周期 boolean
     */
    public final static String PLUS_MEMBER_INFO_ALL = BASE_PREFIX + "PLUS_MEMBER_INFO_ALL_NEW";


    /**
     * 控制是否给用户展示续费弹窗
     */
    public final static String PLUS_MEMBER_RENEW_ALERT = BASE_PREFIX + "PLUS_MEMBER_RENEW_ALERT";

    public final static String ZERO = "0";

    public final static String ONE = "1";

    public final static String PLUS_RENEW_DATA = BASE_PREFIX + "PLUS_RENEW_DATA_";


    /**
     * 成功卡是否能买
     */
    public final static String PLUS_SUC_CAN_BUY = BASE_PREFIX + "PLUS_SUC_CAN_BUY_";


    /**
     * 虚拟商品订单提交 -- 预防重提
     */
    public final static String COMMIT_VIRTUAL_ORDER_KEY = BASE_PREFIX + "COMMIT_VIRTUAL_ORDER_";

    /**
     * 权益0元发放虚拟商品订单提交 -- 预防重提
     */
    public final static String COMMIT_LYFF_VIRTUAL_ORDER_KEY =
            BASE_PREFIX + "COMMIT_LYFF_VIRTUAL_ORDER_";

    /**
     * 会员商品订单提交 -- 预防重提
     */
    public final static String COMMIT_PLUS_PRODUCT_ORDER_KEY =
            BASE_PREFIX + "COMMIT_PLUS_PRODUCT_ORDER_";

    /**
     * 0元商品订单提交 -- 预防重提
     */
    public final static String COMMIT_LYSP_ORDER_KEY = BASE_PREFIX + "COMMIT_LYSP_ORDER_";

    /**
     * 方案下的虚拟商品列表
     */
    public final static String PROGRAM_VIRTUAL_PRODUCTS_NEW =
            BASE_PREFIX + "PROGRAM_VIRTUAL_PRODUCTS_NEW_";

    /**
     * 权益页用户的虚拟商品列表
     */
    public final static String MEMBER_VIRTUAL_PRODUCTS = BASE_PREFIX + "MEMBER_VIRTUAL_PRODUCTS_";


    /**
     * 虚拟商品详情
     */
    public final static String PROGRAM_PRODUCT_DETAIL = BASE_PREFIX + "PROGRAM_PRODUCT_DETAIL_";


    /**
     * 90天内最新的一笔有效借款订单(501、502、611、210、220、224)
     */
    public final static String PLUS_LAST_ONE_LOAN_ORDER = BASE_PREFIX + "PLUS_LAST_ONE_LOAN_ORDER_";

    /**
     * 方案基本信息获取
     */
    public final static String PLUS_BASE_INFO = BASE_PREFIX + "PLUS_BASE_INFO_";

    /**
     * 取消重复支付订单 -- 预防重点
     */
    public final static String CANCEL_REPEAT_ORDER = BASE_PREFIX + "CANCEL_REPEAT_ORDER_";


    /**
     * 后付款订单
     */
    public final static String PLUS_MEMBER_AFTER_ORDER = BASE_PREFIX + "PLUS_MEMBER_AFTER_ORDER_";

    /**
     * 降息卡营销页
     */
    public final static String PLUS_RATE_MARKET_PAGE_KEY =
            BASE_PREFIX + "PLUS_RATE_MARKET_PAGE_KEY_";

    /**
     * 开通会员卡--防止重点
     */
    public final static String OPEN_PLUS_MEMBER_KEY = BASE_PREFIX + "OPEN_PLUS_MEMBER_KEY_";

    /**
     * 认证提额页数据
     */
    public final static String AUTH_RAISE_SWITCH_RESULT = BASE_PREFIX + "AUTH_RAISE_SWITCH_RESULT_";

    /**
     * 认证提额页数据
     */
    public final static String AUTH_RAISE_SWITCH = BASE_PREFIX + "AUTH_RAISE_SWITCH";

    /**
     * 是否符合折扣条件缓存
     */
    public final static String DISCOUNT_CONDITION_RESULT =
            BASE_PREFIX + "DISCOUNT_CONDITION_RESULT_";

    /**
     * 用户标签缓存
     */
    public final static String MEMBER_SIGN_INFO_ALL = BASE_PREFIX + "MEMBER_SIGN_INFO_ALL_";

    /**
     * 借款首页缓存
     */
    public final static String LOAN_PAGE_NO_TARGET = BASE_PREFIX + "LOAN_PAGE_NO_TARGET_";

    /**
     * 会员卡是否能买
     */
    public final static String PLUS_CAN_BUY = BASE_PREFIX + "PLUS_CAN_BUY_";

    /**
     * 会员费用，折扣金额
     */
    public final static String PLUS_MARKET_AMOUNT_INFO = BASE_PREFIX + "PLUS_MARKET_AMOUNT_INFO__";

    /**
     * 划扣失败短信发送计数
     */
    public final static String MEMBER_DEDUCT_FAIL_COUNT = BASE_PREFIX + "MEMBER_DEDUCT_FAIL_COUNT_";

    /**
     * 催收调用支付划扣放重提
     */
    public final static String CS_PAY_EDUCT_KEY = BASE_PREFIX + "CS_PAY_DEDUCT_KEY_";

    /**
     * 下沉用户放弃付款防重提key
     */
    public static final String SINK_USER_CLOSE_ORDER_KEY =
            BASE_PREFIX + "SINK_USER_CLOSE_ORDER_KEY";

    /**
     * 方案详情 只包含 基本信息 + 权益信息 缓存
     */
    public static final String PLUS_PROGRAM_COMMON_INFO_CACHE =
            BASE_PREFIX + "PLUS_PROGRAM_COMMON_INFO_CACHE_";

    /**
     * 区间还款优惠券job上次最大id
     */
    public static final String PLUS_QJHJ_JOB_LAST_MAX_ID = BASE_PREFIX + "plus_qjhk_last_max_id";

    /**
     * 过期取消-比例退款 -- 预防重点
     */
    public final static String PAST_CANCEL_REPEAT_ORDER = BASE_PREFIX + "PAST_CANCEL_REPEAT_ORDER_";

    /**
     * 结清返现-会员退费 -- 预防重点
     */
    public final static String CASHBACK_REPEAT_ORDER = BASE_PREFIX + "CASHBACK_REPEAT_ORDER_";

    /**
     * 过期取消-打款job -- 预防重跑
     */
    public final static String PAST_CANCEL_JOB_REFUND = BASE_PREFIX + "PAST_CANCEL_JOB_REFUND_";

    /**
     * 结清返现-打款job -- 预防重跑
     */
    public final static String MICRO_DEFRAY_JOB = BASE_PREFIX + "MICRO_DEFRAY_JOB_";

    /**
     * 过期取消-查证打款结果 -- 预防重跑
     */
    public final static String PAST_CANCEL_JOB_VERIFY = BASE_PREFIX + "PAST_CANCEL_JOB_VERIFY_";


    /**
     * 添加差异化定价防重提key
     */
    public static final String ADD_DIFF_PRICE = BASE_PREFIX + "PRICE_CONFIG:ADD_DIFF_PRICE_";

    /**
     * 修改差异化定价防重提key
     */
    public static final String UPD_DIFF_PRICE = BASE_PREFIX + "PRICE_CONFIG:UPD_DIFF_PRICE_";

    /**
     * 添加默认方案价防重提key
     */
    public static final String ADD_DEFAULT_PRICE = BASE_PREFIX + "PRICE_CONFIG:ADD_DEFAULT_PRICE_";

    /**
     * 修改默认方案价防重提key
     */
    public static final String UPD_DEFAULT_PRICE = BASE_PREFIX + "PRICE_CONFIG:UPD_DEFAULT_PRICE_";

    /**
     * 渠道管理添加防重提
     */
    public static final String PLUS_CHANNEL_MANAGE_ADD_LOCK =
            BASE_PREFIX + "plus_channel_manage_add_lock_";

    /**
     * 渠道管理修改防重提
     */
    public static final String PLUS_CHANNEL_MANAGE_EDIT_LOCK =
            BASE_PREFIX + "plus_channel_manage_edit_lock_";

    /**
     * 渠道管理上下架防重提
     */
    public static final String PLUS_CHANNEL_MANAGE_ISSUE_LOCK =
            BASE_PREFIX + "plus_channel_manage_issue_lock_";

    /**
     * 缓存差异化定价配置key
     */
    public static final String DIFF_PRICE_CONFIG_KEY =
            BASE_PREFIX + "PRICE_CONFIG:DIFF_PRICE_CONFIG_%s_%s";
    /**
     * 缓存差异化定价配置key 如果渠道标识不是宜口袋 key后面拼接渠道标识
     */
    public static final String DIFF_PRICE_CONFIG_KEY_NEW =
            BASE_PREFIX + "PRICE_CONFIG:DIFF_PRICE_CONFIG_%s_%s_%s";

    /**
     * 缓存差异化提额等级顺序配置key
     */
    public static final String PRICE_GRADE_CONFIG_KEY =
            BASE_PREFIX + "PRICE_CONFIG:PRICE_GRADE_CONFIG_%s_%s";
    public static final String PRICE_GRADE_CONFIG_KEY_NEW =
            BASE_PREFIX + "PRICE_CONFIG:PRICE_GRADE_CONFIG_%s_%s_%s";
    /**
     * 缓存默认方案价配置key
     */
    public static final String DEFAULT_PRICE_CONFIG_KEY =
            BASE_PREFIX + "PRICE_CONFIG:DEFAULT_PRICE_CONFIG_%s_%s";
    /**
     * 缓存默认方案价配置key 如果渠道标识不是宜口袋 key后面拼接渠道标识
     */
    public static final String DEFAULT_PRICE_CONFIG_KEY_NEW =
            BASE_PREFIX + "PRICE_CONFIG:DEFAULT_PRICE_CONFIG_%s_%s_%s";

    /**
     * 复用渠道方案防重提key
     */
    public static final String MULTIPLEX_PROGRAM_RESUBMIT_KEY = BASE_PREFIX + "multiplex_program_";

    /**
     * 渠道缓存
     */
    public static final String CHANNEL_CACHE = BASE_PREFIX + "plus_channel_manage_cache_";

    /**
     * 代付取消-比例退款 -- 预防重点
     */
    public static final String ANOTHER_PAY_CANCEL_REPEAT_ORDER =
            BASE_PREFIX + "ANOTHER_PAY_CANCEL_REPEAT_ORDER_";

    /**
     * 重提客群-用户保存确认结果
     */
    public final static String SAVE_RESUBMIT_GROUP_RECORD =
            BASE_PREFIX + "SAVE_RESUBMIT_GROUP_RECORD";

    /**
     * 方案下的联名卡虚拟权益
     */
    public final static String PROGRAM_LMK_VIRTUAL      = BASE_PREFIX + "PROGRAM_LMK_VIRTUAL_";
    /**
     * 方案关联的一元购商品缓存
     */
    public final static String PROGRAM_ONE_PRODUCTS_KEY = BASE_PREFIX + "PROGRAM_ONE_PRODUCTS_KEY_";

    /**
     * 全部开通的会员校验购买限制缓存
     */
    public final static String PLUS_PROFIT_LIMIT_ALL = BASE_PREFIX + "PLUS_PROFIT_LIMIT_ALL_";

    /**
     * 权益页校验购买限制缓存
     */
    public final static String PLUS_PROFIT_LIMIT = BASE_PREFIX + "PLUS_PROFIT_LIMIT_";

    /**
     * 全部商品id集合
     */
    public final static String PLUS_ALL_YYG_PRODUCT_ID = BASE_PREFIX + "PLUS_ALL_YYG_PRODUCT_ID";

    /**
     * 联名卡充值防重提key
     */
    public final static String PLUS_LMK_RECHARGE_RESUBMIT_KEY =
            BASE_PREFIX + "PLUS_LMK_RECHARGE_RESUBMIT_";

    /**
     * 联名卡权益创单时缓存
     */
    public final static String PLUS_LMK_CREATE_CACHE  = BASE_PREFIX + "PLUS_LMK_CREATE_CACHE_";
    /**
     * 会员商品防重提lockKey
     */
    public final static String HALF_PRICE_SUBMIT_LOCK = BASE_PREFIX + "HALF_PRICE_SUBMIT_LOCK_";

    /**
     * 用户当前周期会员信息列表
     */
    public final static String MEMBER_PLUS_LIST = BASE_PREFIX + "MEMBER_PLUS_LIST_";

    /**
     * 用户会员身份信息-新
     */
    public final static String MEMBER_PLUS_DETAIL_NEW = BASE_PREFIX + "MEMBER_PLUS_DETAIL_NEW_";

    /**
     * 添加渠道短信配置防重提
     */
    public static final String PLUS_SMS_NODE_MANAGE_ADD_LOCK  =
            BASE_PREFIX + "plus_sms_node_manage_add_lock_";
    /**
     * 修改渠道短信配置防重提
     */
    public static final String PLUS_SMS_NODE_MANAGE_EDIT_LOCK =
            BASE_PREFIX + "plus_sms_node_manage_edit_lock_";

    /**
     * 开启或禁用渠道短信配置防重提
     */
    public static final String PLUS_SMS_NODE_MANAGE_ISSUE_LOCK =
            BASE_PREFIX + "plus_sms_node_manage_issue_lock_";

    /**
     * 根据渠道、会员类型、节点、获取配置
     */
    public static final String PLUS_SMS_CONFIG_MANAGE =
            BASE_PREFIX + "plus_sms_config_manage_cache_";


    /**
     * 获取订单信息缓存key
     */
    public static final String PLUS_SMS_CONFIG_ORDER_MANAGE =
            BASE_PREFIX + "plus_sms_config_order_cache_";

    /**
     * 会员分流配置
     */
    public static final String PLUS_SHUNT_CONTROL = BASE_PREFIX + "plus_shunt_control";

    /**
     * 借款订单放款轮播
     */
    public static final String LOAN_ORDER_RECORDS = BASE_PREFIX + "plus_loan_order_records";

    /**
     * 会员分流供应商每天已下单总笔数-计划
     */
    public static final String PLUS_PLAN_SHUNT_ORDER_COUNT =
            BASE_PREFIX + "shunt_plan_use_order_count";


    /**
     * 会员分流供应商每天已下单总金额-计划
     */
    public static final String PLUS_PLAN_SHUNT_ORDER_AMOUNT =
            BASE_PREFIX + "shunt_plan_use_order_amount";

    /**
     * 会员分流供应商渠道业务场景每天已下单总金额-计划
     */
    public static final String PLUS_PLAN_SHUNT_CHANNEL_ORDER_AMOUNT = BASE_PREFIX + "shunt_channel_plan_use_order_amount";

    /**
     * 会员分流供应商每天已下单总笔数-实际
     */
    public static final String PLUS_SHUNT_ORDER_COUNT = BASE_PREFIX + "shunt_use_order_count";


    /**
     * 会员分流供应商每天已下单总金额-实际
     */
    public static final String PLUS_SHUNT_ORDER_AMOUNT = BASE_PREFIX + "shunt_use_order_amount";

    /**
     * 会员分流供应商渠道业务场景每天已下单总金额-实际
     */
    public static final String PLUS_SHUNT_ORDER_CHANNEL_SCENE_AMOUNT = BASE_PREFIX + "shunt_use_order_channel_scene_amount";

    /**
     * 黑卡联登token
     */
    public static final String PLUS_OAKVIP_USER_TOKEN = BASE_PREFIX + "oakvip_user_token_";

    /**
     * 分流比例配置机器ip缓存，用来判断哪台机器需要刷新路由策略
     */
    public static final String PLUS_SHUNT_CONFIG_UPD = BASE_PREFIX + "plus_shunt_config_upd_";

    /**
     * 入账防重提
     */
    public static final String PLUS_INCOME_LOCK = BASE_PREFIX + "plus_income_lock_";

    /**
     * 会员订单关联关系
     */
    public static final String PLUS_RELATION = BASE_PREFIX + "plus_relation";

    /**
     * 渠道管理配置缓存
     */
    public static final String CHANNEL_MANAGER_CACHE = "super_plus:plus_channel_manage_config_cache_";

    /**
     * 方案下的虚拟商品列表-多级分类
     */
    public final static String PROGRAM_VIRTUAL_PRODUCTS_LEVEL =
            BASE_PREFIX + "PROGRAM_VIRTUAL_PRODUCTS_LEVEL_";

    /**
     * 方案下的商品列表
     */
    public static final String PROGRAM_PLUS_PRODUCTS = BASE_PREFIX + "PROGRAM_PLUS_PRODUCTS_";

    /**
     * 权益页用户的会员商品列表
     */
    public final static String MEMBER_PLUS_PRODUCTS = BASE_PREFIX + "MEMBER_PLUS_PRODUCTS_";

    /**
     * 融担咨询卡创单入参
     */
    public final static String RDZX_CREATE_EVET = BASE_PREFIX + "RDZX_CREATE_EVENT_";

    /**
     * 区间还款优惠折扣配置
     */
    public static final String PROGRAM_QJHK_CACHE = BASE_PREFIX + "PROGRAM_QJHK_%s";

    /**
     * 区间还款优惠配置
     */
    public static final String QJHK_COUPON_CONFIG = BASE_PREFIX + "QJHK_COUPON_CONFIG";

    /**
     * 导流页会员营销缓存
     */
    public final static String LEAD_PAGE_NO_TARGET = BASE_PREFIX + "LEAD_PAGE_NO_TARGET_";

    /**
     * 订单状态变更：购物返现消息内容
     */
    public final static String GWFX_ORDER_MQ_INFO = BASE_PREFIX + "GWFX_ORDER_MQ_INFO_";

    /**
     * 营销开关缓存
     */
    public static final String MARKET_SWITCH = BASE_PREFIX + "MARKET_SWITCH_";

    /**
     * 用户折扣缓存
     */
    public static final String MEMBER_DISCOUNT = BASE_PREFIX + "member_discount";

    /**
     * 用户新会员折扣营销弹窗
     */
    public static final String MEMBER_DISCOUNT_ALERT = BASE_PREFIX + "member_discount_alert";

    /**
     * 方案下虚拟商品列表(目前只有权益0元发放)
     */
    public final static String PROGRAM_VIRTUAL_GOODS = BASE_PREFIX + "PROGRAM_VIRTUAL_GOODS_%s_%s";

    /**
     * 增加短信模板配置
     */
    public final static String ADD_SMS_TEM = BASE_PREFIX + "ADD_SMS_TEMP_";

    /**
     * 融担咨询卡权益开关
     */
    public static final String PLUS_PROGRAM_RDZX_EQUITY_SWITCH =
            BASE_PREFIX + "PLUS_PROGRAM_RDZX_EQUITY_SWITCH_%s";

    /**
     * 还款返现配置
     */
    public final static String PROGRAM_REPAY_CASH_BACK_CONFIG =
            BASE_PREFIX + "PROGRAM_REPAY_CASH_BACK_CONFIG_%s";

    /**
     * 还款返现-打款job -- 预防重跑
     */
    public final static String HKFX_MICRO_DEFRAY_JOB = BASE_PREFIX + "HKFX_MICRO_DEFRAY_JOB_";

    /**
     * 新增分流主体防重提
     */
    public final static String ADD_SUPPLIER_RESUBMIT = BASE_PREFIX + "PLUS_SHUNT_ADD_";

    /**
     * 编辑分流主体防重提
     */
    public final static String EDIT_SUPPLIER_RESUBMIT = BASE_PREFIX + "PLUS_SHUNT_EDIT_";

    /**
     * 启用分流主体防重提
     */
    public final static String ENABLE_SUPPLIER_RESUBMIT = BASE_PREFIX + "PLUS_SHUNT_ENABLE_";

    /**
     * 停用分流主体防重提
     */
    public final static String DISABLE_SUPPLIER_RESUBMIT = BASE_PREFIX + "PLUS_SHUNT_DISABLE_";

    /**
     * 分流主体缓存
     */
    public final static String SUPPLIER_CONFIG = BASE_PREFIX + "PLUS_SHUNT_SUPPLIER_";

    /**
     * 编辑分流共配置防重提
     */
    public final static String EDIT_SUPPLIER_PUBLIC = BASE_PREFIX + "EDIT_SUPPLIER_PUBLIC";

    /**
     * 分流公共配置
     */
    public final static String SUPPLIER_PUBLIC_CONFIG = BASE_PREFIX + "PLUS_SHUNT_PUBLIC_CONFIG";

    /**
     * 新增分流路由配置防重提
     */
    public final static String ADD_SHUNT_ROUTE = BASE_PREFIX + "PLUS_SHUNT_ROUTE_ADD_";

    /**
     * 编辑分流路由配置防重提
     */
    public final static String EDIT_SHUNT_ROUTE = BASE_PREFIX + "PLUS_SHUNT_ROUTE_EDIT_";

    /**
     * 分流路由配置启用
     */
    public final static String ENABLE_SHUNT_ROUTE = BASE_PREFIX + "PLUS_SHUNT_ROUTE_ENABLE_";

    /**
     * 分流路由配置停用
     */
    public final static String DISABLE_SHUNT_ROUTE = BASE_PREFIX + "PLUS_SHUNT_ROUTE_DISABLE_";

    /**
     * 分流路由配置列表
     */
    public final static String PLUS_SHUNT_ROUTE = BASE_PREFIX + "PLUS_SHUNT_ROUTE";

    /**
     * 区间还款配置优惠券刷新缓存
     */
    public final static String QJHK_COUPON_CONFIG_REFRESH = "QJHK_COUPON_CONFIG_REFRESH";

    /**
     * 新增清分主体防重提
     */
    public final static String ADD_SEPARATE_SUPPLIER_RESUBMIT = BASE_PREFIX + "PLUS_SHUNT_ADD_SEPARATE_";

    /**
     * 编辑清分主体防重提
     */
    public final static String EDIT_SEPARATE_SUPPLIER_RESUBMIT = BASE_PREFIX + "PLUS_SHUNT_EDIT_SEPARATE_";

    /**
     * 停用清分主体防重提
     */
    public final static String DISABLE_SEPARATE_SUPPLIER_RESUBMIT =
            BASE_PREFIX + "PLUS_SHUNT_SEPARATE_DISABLE_";

    /**
     * 启用清分主体防重提
     */
    public final static String ENABLE_SEPARATE_SUPPLIER_RESUBMIT = BASE_PREFIX + "PLUS_SHUNT_SEPARATE_ENABLE_";

    /**
     * 原路退款防重提
     */
    public final static String PLUS_ORDER_REFUND_APPLY = BASE_PREFIX + "PLUS_ORDER_REFUND_APPLY_";

    /**
     * 处理订单中心退款通知
     */
    public final static String PLUS_REFUND_LISTEN = BASE_PREFIX + "PLUS_REFUND_LISTEN_";
    /**
     * 取消订单
     */
    public final static String PLUS_CANCEL_ORDER = BASE_PREFIX + "plus_cancel_order_";

    /**
     * 订单取消监控
     */
    public final static String PLUS_CANCEL_ORDER_MONITOR_JOB = BASE_PREFIX + "PLUS_CANCEL_ORDER_MONITOR_JOB_";

    /**
     * 订单取消监控-订单
     */
    public final static String PLUS_CANCEL_ORDER_MONITOR_ORDER = BASE_PREFIX + "PLUS_CANCEL_ORDER_MONITOR_ORDER";


    /**
     * 生成对账信息
     */
    public final static String PLUS_INSERT_ORDER_BILL = BASE_PREFIX + "insert_order_bill_";
    /**
     * 补偿融单咨询卡的合同,失败记录的id缓存的key
     */
    public final static String HANDLE_RDZX_FAIL_ID_KEY = RedisConstantPrefix.BASE_PREFIX + "handlerdzx:fail:order:ids";

    /**
     * 查询主动支付信息防重提
     */
    public static final String PLUS_ORDER_QUERY_PAY_INFO = BASE_PREFIX + "PLUS_ORDER_QUERY_PAY_INFO_";

    /**
     * 订单主动支付信息缓存
     */
    public static final String PLUS_ORDER_PAY_INFO = BASE_PREFIX + "PLUS_ORDER_PAY_INFO_";

    /**
     * 后付款会员订单主动支付缓存标记
     */
    public static final String PLUS_ORDER_AFTER_ZD = BASE_PREFIX + "PLUS_ORDER_AFTER_PAY_ZD:";
    /**
     * 后付款会员订单系统代扣缓存标识
     */
    public static final String PLUS_ORDER_AFTER_HK = BASE_PREFIX + "PLUS_ORDER_AFTER_PAY_HK:";

    /**
     * 结算申请单初始化起始id
     */
    public final static String SETTLE_BILL_APPLY_BEGIN_ID = BASE_PREFIX + "SETTLE_BILL_APPLY_BEGIN_ID";

    /**
     * 过期取消记录表初始化起始id
     */
    public final static String PAST_MEMBER_REFUND_RECORD_BEGIN_ID = BASE_PREFIX + "PAST_MEMBER_REFUND_RECORD_BEGIN_ID";
    public final static String PAST_MEMBER_REFUND_RECORD_INSPECT_BEGIN_ID = BASE_PREFIX + "PAST_MEMBER_REFUND_RECORD_INSPECT_BEGIN_ID";

    /**
     * 结算申请单初始化起始id
     */
    public final static String PLUS_SHUNT_SUPPLIER_PAY_BEGIN_ID = BASE_PREFIX + "PLUS_SHUNT_SUPPLIER_PAY_BEGIN_ID";

    /**
     * AI外呼日志记录初始化起始id
     */
    public final static String PLUS_AI_OUTBOUND_INFO_LOG_BEGIN_ID = BASE_PREFIX + "PLUS_AI_OUTBOUND_INFO_LOG_BEGIN_ID";

   /**
    * 存储用户最近一次营销会员的业务场景
    */
    public final static String PLUS_SHUNT_SCENE = BASE_PREFIX + "PLUS_SHUNT_SCENE_%s_%s";
        


}
