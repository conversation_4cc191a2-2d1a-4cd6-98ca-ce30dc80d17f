package com.juzifenqi.plus.config;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import java.util.List;
import java.util.Objects;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2020/1/2 2:09 PM
 */
@Component
@Slf4j
public class ConfigProperties {

    @NacosValue(value= "${juziPlus.plusChannels}", autoRefreshed = true)
    public String plusChannels;

    @NacosValue(value = "${juziPlus.grade}", autoRefreshed = true)
    public String grade;

    /**
     * 生日权益桔豆数量
     */
    @NacosValue(value = "${juziPlus.juziBeanNum}", autoRefreshed = true)
    public Integer juziBeanNum;

    /**
     * 横版竖版切换截止时间
     */
    @NacosValue(value = "${juziPlus.crossEndTime}", autoRefreshed = true)
    public String crossEndTime;

    /**
     * 桔富卡连续包月价格
     * {"R":{"renew":"true","renewPrice":"17.9","popupHeadImg":"https://jzfq2016.oss-cn-beijing.aliyuncs.com/ios/290006937/20200825153438182-orginal.JPG",
     * "secondPopupImg":"https://jzfq2016.oss-cn-beijing.aliyuncs.com/ios/290006937/20200825153438182-orginal.JPG"}}
     */
    @NacosValue(value = "${common.plusProgramInfo}", autoRefreshed = true)
    public String gradeGetProgram;

    /**
     * 信用钱包页-已开通 banner 入口展示图片地址（会员入口）
     * <p>{"E":{"bannerUrl":"www.baidu.com","bannerSkipUrl":"www.baidu.com"},</p>
     * <p>"G":{"bannerUrl":"www.baidu.com","bannerSkipUrl":"www.baidu.com"}}</p>
     */
    @NacosValue(value = "${juziPlus.openBannerUrl}", autoRefreshed = true)
    public String openBannerUrl;

    /**
     * 加速卡落地页地址+营销图片 {"landingPageUrl":"http://url","entranceImg":"http://imgUrl"}
     */
    @NacosValue(value = "${juziPlus.suLandingPageUrl}", autoRefreshed = true)
    public String suMarketingInfo;

    /**
     * 统一入口地址 {"showStatus":0,"skipUrl":""}
     */
    @NacosValue(value = "${juziPlus.entranceUrl}", autoRefreshed = true)
    public String entranceUrl;

    /**
     * 认证成功卡上线时间，格式：2021-05-20 18:00:00
     */
    @NacosValue(value = "${juziPlus.sucCardOnlineTime}", autoRefreshed = true)
    public String sucCardOnlineTime;

    /**
     * 认证成功卡配置信息
     * {"sucLandingPageUrl":"https://mall.juzifenqi.com/vip/juxin/index.html?programId=","sucImgUrl":"https://mallimages.juzifenqi.com/211214/8a684f2b-7255-478e-9278-9543042e655a.png","sucDescription":"新人提额礼包"}
     */
    @NacosValue(value = "${juziPlus.successCardData}", autoRefreshed = true)
    public String successCardData;

    /**
     * 新客失败卡落地页地址 landingPageUrl:https://test2-mall.juzishuke.com/vip/jiasu/index.html?programId=
     */
    @NacosValue(value = "${juziPlus.afLandingPageUrl}", autoRefreshed = true)
    public String afLandingPageUrl;

    /**
     * 新客失败卡图片地址
     */
    @NacosValue(value = "${juziPlus.afImgUrl}", autoRefreshed = true)
    public String afImgUrl;

    /**
     * 重提卡资方放款失败发送短信开发控制
     */
    @NacosValue(value = "${juziPlus.RESUBMIT_SWITCH}", autoRefreshed = true)
    public Integer resubmitSwitch;

    /**
     * 加速卡放款审核中发送短信开发控制
     */
    @NacosValue(value = "${juziPlus.EXPEDITE_SWITCH}", autoRefreshed = true)
    public Integer expediteSwitch;

    /**
     * 策略等级开关  0不开启，非0开启，走差异化定价配置，只针对于桔享卡
     */
    @NacosValue(value = "${juziPlus.STRATEGY_GRADE}", autoRefreshed = true)
    public String strategyGrade;

    /**
     * 会员权益页短链
     */
    @NacosValue(value = "${juziPlus.shortChain}", autoRefreshed = true)
    public String shortChain;

    /**
     * 会员列表查询过期开始时间
     */
    @NacosValue(value = "${juziPlus.orderStartTime}", autoRefreshed = true)
    public String orderStartTime;

    /**
     * 月利率 = 0.24 / 12
     */
    @NacosValue(value = "${juziPlus.afterPayRate}", autoRefreshed = true)
    public String afterPayRate;

    /**
     * 桔策引擎code
     */
    @NacosValue(value = "${juziPlus.engineCode}", autoRefreshed = true)
    public String engineCode;

    /**
     * 加速卡上线时间，格式：2022-07-27 18:00:00
     */
    @NacosValue(value = "${juziPlus.expediteCardOnlineTime}", autoRefreshed = true)
    public String expediteCardOnlineTime;

    //通过卡类型获取短链
    public String getShortChainByType(String type) {
        try {
            log.info("通过卡类型获取短链配置中心数据：{}", shortChain);
            JSONObject jsonObject = JSONObject.parseObject(shortChain);
            if (Objects.isNull(jsonObject)) {
                return "";
            }
            return jsonObject.getString(type);
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 会员未绑定借款订单超时时间
     */
    @NacosValue(value = "${juziPlus.bindTimeOutMin}", autoRefreshed = true)
    public Integer bindTimeOutMin;


    /**
     * 验收配置开关
     * <P>！！！！！千万要保证只有一个地方使用，否则可能影响其他逻辑！！！！！！！</P>
     */
    @NacosValue(value = "${juziPlus.targetUsers}", autoRefreshed = true)
    public String targetUsers;
    //使用方式
    //    String targetUsers = configProperties.targetUsers;
    //        if (!StringUtils.isEmpty(targetUsers) && !targetUsers.contains(userId.toString())) {
    //        log.info("非上线目标用户 userId:{}，targetUsers:{}", userId, targetUsers);
    //        return false;
    //    }

    /**
     * 标签key:{configId:key1,configId:key2,configId:key3}
     * 如：{"5":"singcode1234565","1":"singcode09876","4":"singcode666888"}
     */
    @NacosValue(value = "${juziPlus.plusSignKeys}", autoRefreshed = true)
    public String plusSignKeys;

    /**
     * errCode和延迟退款logType的关系
     */
    @NacosValue(value = "${juziPlus.logTypeRelation}", autoRefreshed = true)
    public String logTypeRelation;

    @NacosValue(value = "${juziPlus.nodeCode}", autoRefreshed = true)
    public Integer nodeCode;

    /**
     * 重提客群借款订单金额
     */
    @NacosValue(value = "${juziPlus.loanOrderAmount}", autoRefreshed = true)
    public String loanOrderAmount;

    /**
     * 重提客群借款期数
     */
    @NacosValue(value = "${juziPlus.stageNum}", autoRefreshed = true)
    public Integer stageNum;

    /**
     * 重提客群默认月利率
     */
    @NacosValue(value = "${juziPlus.defaultLoanRate}", autoRefreshed = true)
    public String defaultLoanRate;

    /**
     * 桔会卡落地页短链
     */
    @NacosValue(value = "${juziPlus.jhUrl}", autoRefreshed = true)
    public String jhUrl;

    /**
     * Ai机器人appKey
     */
    @NacosValue(value = "${juziPlus.aiAppKey}", autoRefreshed = true)
    public String appKey;

    /**
     * Ai机器人appSecret
     */
    @NacosValue(value = "${juziPlus.aiAppSecret}", autoRefreshed = true)
    public String appSecret;

    /**
     * Ai机器人任务ID
     */
    @NacosValue(value = "${juziPlus.taskId}", autoRefreshed = true)
    public Integer taskId;

    /**
     * 桔子合同编号
     */
    @NacosValue(value = "${juziPlus.juziContractNo:TEMP20221116181636667,TEMP1693822861476585472}", autoRefreshed = true)
    public String juziContractNo;

    /**
     * 获取银行卡信息url
     */
    @NacosValue(value = "${juziPlus.cardUrl}", autoRefreshed = true)
    public String cardUrl;

    /**
     * 支付旧系统url
     */
    @NacosValue(value = "${juziPlus.payUrl}", autoRefreshed = true)
    public String payUrl;

    /**
     * 桔策url
     */
    @NacosValue(value = "${juziPlus.orangeUrl}", autoRefreshed = true)
    public String orangeUrl;

    /**
     * 风控差异化利率url
     */
    @NacosValue(value = "${juziPlus.rmsRateUrl}", autoRefreshed = true)
    public String rmsRateUrl;

    /**
     * 飞书url
     */
    @NacosValue(value = "${juziPlus.feishuUrl}", autoRefreshed = true)
    public String feishuUrl;


    /**
     * 飞书Secret
     */
    @NacosValue(value = "${juziPlus.feishuSecret}", autoRefreshed = true)
    public String feishuSecret;

    /**
     * 飞书商户额度报警url
     */
    @NacosValue(value = "${juziPlus.feishuMerLimitUrl}", autoRefreshed = true)
    public String feishuMerLimitUrl;

    /**
     * 风控url
     */
    @NacosValue(value = "${juziPlus.riskUrl}", autoRefreshed = true)
    public String riskUrl;

    /**
     * 标签url
     */
    @NacosValue(value = "${juziPlus.signUrl}", autoRefreshed = true)
    public String signUrl;

    /**
     * 融担卡资方超24利率配置
     * <p>示例：{183:0.2345,239:0.2345}</p>
     */
    @NacosValue(value = "${juziPlus.rdzxRate}", autoRefreshed = true)
    public String rdzxRateConfig;

    /**
     * 合同下载临时路径
     */
    @NacosValue(value = "${juziPlus.contractDownPath}", autoRefreshed = true)
    public String contractDownPath;

    /**
     * 短链url
     */
    @NacosValue(value = "${juziPlus.shortUrl}", autoRefreshed = true)
    public String shortUrl;

    /**
     * 短链accessKey
     */
    @NacosValue(value = "${juziPlus.shortUrlAccessKey}", autoRefreshed = true)
    public String shortUrlAccessKey;

    /**
     * 短链域名类型
     */
    @NacosValue(value = "${juziPlus.shortUrlDomainType}", autoRefreshed = true)
    public String shortUrlDomainType;

    /**
     * 权益0元发放-权益页面地址
     */
    @NacosValue(value = "${juziPlus.lyffUrl}", autoRefreshed = true)
    public String lyffUrl;

    @NacosValue(value = "${juziPlus.engineManageUrl}", autoRefreshed = true)
    public String engineManageUrl;

    /**
     * 小额月卡客群code
     */
    @NacosValue(value = "${juziPlus.xeykCustomerGroupCode:VIPshowxiaoeyueka}", autoRefreshed = true)
    public String xeykCustomerGroupCode;

    /**
     * 资匹域名
     */
    @NacosValue(value = "${juziPlus.capUrl}", autoRefreshed = true)
    public String capUrl;

    /**
     * 桔子服务协议合同编号
     */
    @NacosValue(value = "${juziPlus.juziServiceContractNo:TEMP20221116181636667}", autoRefreshed = true)
    public String juziServiceContractNo;

    /**
     * 分流方代付商编id
     */
    @NacosValue(value = "${juziPlus.defrayMerchant}", autoRefreshed = true)
    public String defrayMerchant;

    /**
     * 优惠券发放失败报警
     */
    @NacosValue(value = "${juziPlus.couponAlarmSwitch:false}", autoRefreshed = true)
    public boolean couponAlarmSwitch;

    /**
     * 新系统切换用户白名单
     */
    @NacosValue(value = "${juziPlus.switchWhiteUser}", autoRefreshed = true)
    public List<Integer> switchWhiteUser;

    /**
     * 除了还款卡走纯代付退款以外,其他订单的创建时间在这个时间之前的，也走纯代付退款
     */
    @NacosValue(value = "${juziPlus.cDefrayRefundOrderCreateTime:2022-01-01 00:00:00}", autoRefreshed = true)
    public String cDefrayRefundOrderCreateTime;


    /**
     * 补偿处理2024.07-2024.09之间未签署合同的融单咨询卡的订单plus_order_info表里的记录的开始id
     */
    @NacosValue(value = "${manual.handle.rdzxOrder.startId:1}",autoRefreshed = true)
    public Integer manualHandleRdzxOrderstartId;

    /**
     * 补偿处理2024.07-2024.09之间未签署合同的融单咨询卡的订单plus_order_info表里的记录的结束id
     */
    @NacosValue(value = "${manual.handle.rdzxOrder.maxId:23519382}",autoRefreshed = true)
    public Integer manualHandleRdzxOrderMaxId;

    /**
     * 补偿处理2024.07-2024.09之间未签署合同的融单咨询卡的订单,每批次的大小
     */
    @NacosValue(value = "${manual.handle.rdzxOrder.batchSize:100}",autoRefreshed = true)
    public Integer manualHandleRdzxOrderBatchSize;

    /**
     * 补偿处理2024.07-2024.09之间未签署的合同编号
     */
    @NacosValue(value = "${manual.handle.rdzxOrder.contract.templateCodes:TEMP1594985527931568128}",autoRefreshed = true)
    public String manualHandleRdzxOrderContractTemplateCodes;

    /**
     * 补偿处理需要处理的总的数据量
     */
    @NacosValue(value = "${manual.handle.rdzxOrder.totalnum:953689}",autoRefreshed = true)
    public String manualHandleRdzxOrderTotalNum;

    /**
     * 分流异常、开关关闭、配置为空的情况下的兜底分流主体
     */
    @NacosValue(value = "${juziPlus.defaultSupplierId:}", autoRefreshed = true)
    public Integer defaultSupplierId;

    /**
     * 融担卡默认分流主体
     */
    @NacosValue(value = "${juziPlus.defaultRdzxSupplierId:}", autoRefreshed = true)
    public Integer defaultRdzxSupplierId;

    /**
     * 会员后付款订单支付锁时间设置(秒)
     */
    @NacosValue(value = "${juziPlus.afterPay.lock:60}", autoRefreshed = true)
    public Integer afterPayLockTime;


    @NacosValue(value = "${alert.exclude:}")
    public String alertExclude;

    @NacosValue(value = "${plus.rdzx.discount:1}")
    public String rdzxDiscountRate;

    @NacosValue(value = "${zfb.refund.delay:10}")
    public Integer zfbRefundDelay;

    /**
     * 支付中状态的订单退款间隔时间(分钟)
     */
    @NacosValue(value = "${juziPlus.pay.interval:1}", autoRefreshed = true)
    public Integer payInterval;

    /**
     * 单笔单批渠道
     */
    @NacosValue(value = "${juziPlus.auth.channel:803004}", autoRefreshed = true)
    public String authChannel;

    /**
     * 会员分流开关, 按渠道控制,包含渠道，走新逻辑。反之，走老逻辑
     */
    @NacosValue(value = "${juziPlus.shunt.channel:11}", autoRefreshed = true)
    public String shuntChannelSwitch;


    /**
     * 清分主体开关, 开启查旧表
     */
    @NacosValue(value = "${juziPlus.shunt.separateSwitch:true}", autoRefreshed = true)
    public boolean shuntSeparateSwitch;

    /**
     * 会员商户账户报警待处理列表
     */
    @NacosValue(value = "${juziPlus.shunt.payAlarmPendingList}", autoRefreshed = true)
    public String payAlarmPendingList;


    /**
     * 会员商户账户报警待处理列表
     */
    @NacosValue(value = "${juziPlus.overdue.cancelUsers:}", autoRefreshed = true)
    public String overdueCancelUsers;

    /**
     * 会员退款监控超时时间 （单位：小时）
     * 监控超过超时时间的未退款会员订单
     */
    @NacosValue(value = "${juziPlus.refundMonitorTimeout:12}", autoRefreshed = true)
    public Integer refundMonitorTimeout;

}

