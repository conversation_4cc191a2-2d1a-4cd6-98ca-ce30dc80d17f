package com.juzifenqi.plus.enums.market;

import lombok.Getter;

/**
 * 会员营销类型
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/22 19:58
 */
@Getter
public enum ShowTypeEnum {
    NO_SHOW(0, 0, "不展示"), JX(1, 1, "桔享卡"), QUOTA(2, 4, "固额卡"), EXP(3, 5,
            "加速卡"), RESUBMIT(4, 6, "重提卡"), SUC(6, 8, "新人卡"), XEYK(12, 12, "小额月卡"), JS(
            14, 14, "桔省卡"), YT(13, 15, "宜通卡"), YK(16, 16, "会员月卡");

    /**
     * 前端依据此字段决定展示营销的卡的样式
     */
    private final Integer code;
    /**
     * 对应会员类型id
     */
    private final Integer configId;
    /**
     * 说明
     */
    private final String  msg;

    ShowTypeEnum(Integer code, Integer configId, String msg) {
        this.code = code;
        this.configId = configId;
        this.msg = msg;
    }

    public static Integer getShowTypeByConfigId(Integer configId) {
        if (configId == null) {
            return NO_SHOW.getCode();
        }
        for (ShowTypeEnum value : ShowTypeEnum.values()) {
            if (value.getConfigId().equals(configId)) {
                return value.getCode();
            }
        }
        return NO_SHOW.getCode();
    }
}
